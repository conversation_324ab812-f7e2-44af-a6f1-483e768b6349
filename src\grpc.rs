use crate::redis_lib::RedisClient;
use crate::auth::AuthService;
use crate::write_consistency::{WriteConsistency, WriteConsistencyChecker, WriteOperation, SiteReplicationManager};
use std::sync::Arc;
use tokio::sync::RwLock;
use tonic::{Request, Response, Status};
use rustycluster::key_value_service_server::{KeyValueService, KeyValueServiceServer};
use rustycluster::*;
use log::{error, info, debug, warn};
use futures::future::join_all;
use tonic::transport::Channel;
use rustycluster::key_value_service_client::KeyValueServiceClient;
use std::collections::HashMap;
use std::time::Duration;
use tokio::time::{sleep, interval};
use std::sync::atomic::{AtomicUsize, Ordering};
use std::hash::{<PERSON>h, <PERSON><PERSON>};
use std::collections::hash_map::DefaultHasher;
use tokio::sync::mpsc;
use parking_lot::Mutex;
pub mod rustycluster {
    tonic::include_proto!("rustycluster");
}



pub struct PooledClient {
    clients: Vec<KeyValueServiceClient<Channel>>,
    current: AtomicUsize,
}

impl PooledClient {
    pub async fn new(size: usize, node: &str) -> Result<Self, tonic::transport::Error> {
        Self::new_with_mode(size, node, true).await // Default to physical connections
    }

    pub async fn new_with_mode(size: usize, node: &str, use_physical_connections: bool) -> Result<Self, tonic::transport::Error> {
        if use_physical_connections {
            info!("Creating connection pool for node {} with {} physical TCP connections", node, size);

            // Create multiple physical connections for true connection pooling
            let mut clients = Vec::with_capacity(size);

            for i in 0..size {
                // Create a separate channel (TCP connection) for each client
                let channel = tonic::transport::Channel::from_shared(node.to_string())
                    .unwrap()
                    .tcp_nodelay(true) // Disable Nagle's algorithm for better latency
                    .connect()
                    .await?;

                let client = KeyValueServiceClient::new(channel);
                clients.push(client);

                if i == 0 || (i + 1) % 50 == 0 || i == size - 1 {
                    debug!("Created physical connection {}/{} for node {}", i + 1, size, node);
                }
            }

            info!("Successfully created connection pool for {} with {} physical TCP connections", node, size);

            Ok(Self {
                clients,
                current: AtomicUsize::new(0),
            })
        } else {
            info!("Creating connection pool for node {} with {} logical clients (HTTP/2 multiplexing)", node, size);

            // Create a single channel with HTTP/2 multiplexing
            let channel = tonic::transport::Channel::from_shared(node.to_string())
                .unwrap()
                .tcp_nodelay(true) // Disable Nagle's algorithm for better latency
                .connect()
                .await?;

            info!("Successfully established HTTP/2 connection to {}", node);

            // Create multiple clients sharing the same channel for better performance
            let mut clients = Vec::with_capacity(size);
            for i in 0..size {
                let client = KeyValueServiceClient::new(channel.clone());
                clients.push(client);
                if i == 0 || (i + 1) % 50 == 0 || i == size - 1 {
                    debug!("Created logical client {}/{} for node {}", i + 1, size, node);
                }
            }

            info!("Successfully created connection pool for {} with {} logical clients sharing 1 HTTP/2 connection", node, size);

            Ok(Self {
                clients,
                current: AtomicUsize::new(0),
            })
        }
    }

    pub fn next_client(&mut self) -> KeyValueServiceClient<Channel> {
        // Use atomic operations for thread safety without locking
        // This is a lock-free approach that scales better under high concurrency
        let current = self.current.fetch_add(1, Ordering::Relaxed) % self.clients.len();
        self.clients[current].clone()
    }
}

// Connection pool for secondary nodes
struct SecondaryNodePool {
    clients: HashMap<String, PooledClient>,
    max_retries: u32,
    retry_delay: Duration,
    pool_size: usize,
    // Track node availability
    node_availability: HashMap<String, bool>,
    // Last time we checked node availability
    last_availability_check: std::time::Instant,
    // How often to refresh the availability cache (in seconds)
    availability_check_interval: Duration,
}

impl SecondaryNodePool {
    // Get available nodes from the provided list
    fn get_available_nodes(&self, nodes: &[String], count: usize) -> Vec<String> {
        if count == 0 || nodes.is_empty() {
            return Vec::new();
        }

        // Only use cached availability if the cache is fresh
        let use_cached = self.last_availability_check.elapsed() < self.availability_check_interval;

        // If we have a fresh cache, use it directly without checking connections
        if use_cached {
            // First, try to get exactly 'count' available nodes
            let mut available_nodes: Vec<String> = nodes.iter()
                .filter(|node| *self.node_availability.get(*node).unwrap_or(&false))
                .take(count)
                .cloned()
                .collect();

            // If we don't have enough available nodes, add unavailable ones up to count
            if available_nodes.len() < count {
                let unavailable_nodes: Vec<String> = nodes.iter()
                    .filter(|node| !self.node_availability.get(*node).unwrap_or(&false))
                    .take(count - available_nodes.len())
                    .cloned()
                    .collect();

                available_nodes.extend(unavailable_nodes);
            }

            return available_nodes;
        }

        // If cache is stale, just return the first 'count' nodes
        // The health check task will update availability in the background
        nodes.iter().take(count).cloned().collect()
    }
    async fn new(nodes: &[String], max_retries: u32, retry_delay: Duration, pool_size: usize, use_physical_connections: bool) -> Self {
        let mut clients = HashMap::new();
        let mut node_availability = HashMap::new();

        for node in nodes {
            match PooledClient::new_with_mode(pool_size, node, use_physical_connections).await {
                Ok(pooled) => {
                    clients.insert(node.clone(), pooled);
                    node_availability.insert(node.clone(), true);
                    info!("Created connection pool for secondary node {}", node);
                }
                Err(e) => {
                    node_availability.insert(node.clone(), false);
                    error!("Failed to create connection pool for secondary node {}: {}", node, e);
                }
            }
        }
        Self {
            clients,
            max_retries,
            retry_delay,
            pool_size,
            node_availability,
            last_availability_check: std::time::Instant::now(),
            availability_check_interval: Duration::from_secs(10), // Check every 10 seconds
        }
    }

    async fn get_client(&mut self, node: &str) -> Option<KeyValueServiceClient<Channel>> {
        // First, try to get an existing client without creating a new one
        // This is the fast path that should handle most requests
        if let Some(pooled) = self.clients.get_mut(node) {
            // Only update availability status if it's been a while since the last check
            // This reduces the frequency of HashMap operations
            if self.last_availability_check.elapsed() > self.availability_check_interval {
                self.node_availability.insert(node.to_string(), true);
                self.last_availability_check = std::time::Instant::now();
            }
            return Some(pooled.next_client());
        }

        // If we don't have a client for this node, create one with retries
        let node_string = node.to_string();

        // Try to create new client pool with retries
        for attempt in 0..self.max_retries {
            match PooledClient::new(self.pool_size, node).await {
                Ok(mut pooled) => {
                    let client = pooled.next_client();
                    // Insert the pooled client into our map
                    self.clients.insert(node_string.clone(), pooled);
                    // Mark node as available
                    self.node_availability.insert(node_string, true);
                    self.last_availability_check = std::time::Instant::now();
                    return Some(client);
                }
                Err(e) => {
                    // Use exponential backoff for retries
                    let backoff = if attempt > 0 {
                        self.retry_delay.mul_f32(1.5_f32.powi(attempt as i32))
                    } else {
                        self.retry_delay
                    };

                    error!("Failed to create client for node {} (attempt {}/{}): {}. Retrying in {:?}...",
                           node, attempt + 1, self.max_retries, e, backoff);
                    sleep(backoff).await;
                }
            }
        }

        // Mark node as unavailable after all retries failed
        self.node_availability.insert(node_string, false);
        self.last_availability_check = std::time::Instant::now();
        error!("Failed to create client for node {} after {} attempts", node, self.max_retries);
        None
    }
}

// Batch of operations to replicate
struct ReplicationBatch {
    operations: Vec<ReplicationOperation>,
    timestamp: std::time::Instant,
    max_age: std::time::Duration,
}

// Manual implementation of Clone for ReplicationBatch since Instant doesn't implement Clone
impl Clone for ReplicationBatch {
    fn clone(&self) -> Self {
        Self {
            operations: self.operations.clone(),
            timestamp: std::time::Instant::now(), // Create a new timestamp
            max_age: self.max_age,
        }
    }
}

impl ReplicationBatch {
    fn new(operations: Vec<ReplicationOperation>, max_age: Duration) -> Self {
        Self {
            operations,
            timestamp: std::time::Instant::now(),
            max_age,
        }
    }

    fn is_expired(&self) -> bool {
        self.timestamp.elapsed() > self.max_age
    }
}

#[derive(Clone)]
struct ReplicationOperation {
    command: String,
    key: String,
    value: String,
    field: Option<String>,
}

/// Site replication batch collector for WriteOperation
struct SiteReplicationBatchCollector {
    operations: Vec<WriteOperation>,
    max_batch_size: usize,
    last_flush_time: std::time::Instant,
    flush_interval: Duration,
}

impl SiteReplicationBatchCollector {
    fn new(max_batch_size: usize, flush_interval: Duration) -> Self {
        Self {
            operations: Vec::with_capacity(max_batch_size),
            max_batch_size,
            last_flush_time: std::time::Instant::now(),
            flush_interval,
        }
    }

    #[inline]
    fn add_operation(&mut self, operation: WriteOperation) -> bool {
        self.operations.push(operation);
        self.should_flush()
    }

    #[inline]
    fn should_flush(&self) -> bool {
        if self.operations.len() >= self.max_batch_size {
            return true;
        }
        self.last_flush_time.elapsed() >= self.flush_interval
    }

    fn take_batch(&mut self) -> Vec<WriteOperation> {
        let mut batch = Vec::with_capacity(self.operations.len());
        std::mem::swap(&mut batch, &mut self.operations);
        self.operations = Vec::with_capacity(self.max_batch_size);
        self.last_flush_time = std::time::Instant::now();
        batch
    }

    #[inline]
    fn is_empty(&self) -> bool {
        self.operations.is_empty()
    }
}

/// Optimized batch collector for replication operations
///
/// Provides efficient batching with adaptive sizing and minimal allocations
struct BatchCollector {
    operations: Vec<ReplicationOperation>,
    max_batch_size: usize,
    last_flush_time: std::time::Instant,
    flush_interval: Duration,
    // Track statistics for adaptive optimization
    total_operations_processed: usize,
    total_batches_flushed: usize,
}

impl BatchCollector {
    fn new(max_batch_size: usize, flush_interval: Duration) -> Self {
        Self {
            // Pre-allocate with capacity to avoid reallocations
            // Use the exact max_batch_size to avoid over-allocation
            operations: Vec::with_capacity(max_batch_size),
            max_batch_size,
            last_flush_time: std::time::Instant::now(),
            flush_interval,
            total_operations_processed: 0,
            total_batches_flushed: 0,
        }
    }

    /// Add an operation to the batch
    ///
    /// Returns true if the batch should be flushed
    #[inline]
    fn add_operation(&mut self, operation: ReplicationOperation) -> bool {
        self.operations.push(operation);
        self.total_operations_processed += 1;
        self.should_flush()
    }

    /// Check if the batch should be flushed
    ///
    /// Uses a fast path for size check before checking time
    #[inline]
    fn should_flush(&self) -> bool {
        // Check size first as it's faster than checking time
        // This is a hot path, so we optimize it for the common case
        if self.operations.len() >= self.max_batch_size {
            return true;
        }

        // Only check time if size check fails
        self.last_flush_time.elapsed() >= self.flush_interval
    }

    /// Take the current batch and reset the collector
    ///
    /// Uses an optimized approach to minimize allocations
    fn take_batch(&mut self) -> Vec<ReplicationOperation> {
        // Use a more efficient approach to take the batch
        // This avoids allocating a new vector if the existing one is already large
        let mut batch = Vec::with_capacity(self.operations.len());
        std::mem::swap(&mut batch, &mut self.operations);

        // Calculate optimal capacity for future operations based on history
        let optimal_capacity = if self.total_batches_flushed > 0 {
            // Use average batch size as a guide, but cap at max_batch_size
            let avg_batch_size = self.total_operations_processed / self.total_batches_flushed;
            avg_batch_size.min(self.max_batch_size)
        } else {
            // First batch, use max_batch_size
            self.max_batch_size
        };

        // Ensure we have capacity for future operations
        self.operations = Vec::with_capacity(optimal_capacity);
        self.last_flush_time = std::time::Instant::now();
        self.total_batches_flushed += 1;

        batch
    }

    /// Check if the batch is empty
    #[inline]
    fn is_empty(&self) -> bool {
        self.operations.is_empty()
    }

    /// Get the current batch size
    #[inline]
    fn len(&self) -> usize {
        self.operations.len()
    }

    /// Get the time since last flush
    #[inline]
    fn time_since_last_flush(&self) -> Duration {
        self.last_flush_time.elapsed()
    }
}

#[derive(Clone)]
pub struct KeyValueServiceImpl {
    redis_client: RedisClient,
    secondary_nodes: Vec<String>,
    replication_factor: usize,
    read_consistency: usize,
    async_replication: bool,
    node_pool: Arc<RwLock<SecondaryNodePool>>,
    max_retries: u32,
    retry_delay: Duration,
    replication_batch_max_age: Duration,
    batch_collectors: Arc<ShardedBatchCollectors>,
    max_batch_size: usize,
    flush_interval: Duration,
    batch_flush_task_running: Arc<AtomicUsize>,
    op_senders: Arc<HashMap<String, mpsc::Sender<ReplicationOperation>>>,
    chunk_size: usize,
    num_shards: usize,
    auth_service: Arc<AuthService>,
    // Write consistency for synchronous replication to peer Redis nodes
    write_consistency_checker: Option<Arc<WriteConsistencyChecker>>,
    peer_redis_nodes: Vec<String>,
    // Site replication for cross-site data replication
    site_replication_manager: Option<Arc<tokio::sync::Mutex<SiteReplicationManager>>>,
    site_replication_enabled: bool,
    // Site replication batching
    site_replication_collector: Arc<tokio::sync::Mutex<SiteReplicationBatchCollector>>,
    site_replication_sender: Option<mpsc::Sender<WriteOperation>>,
}

impl KeyValueServiceImpl {
    // Authenticate client with username and password
    async fn authenticate(&self, request: Request<AuthenticateRequest>) -> Result<Response<AuthenticateResponse>, Status> {
        let AuthenticateRequest { username, password } = request.into_inner();

        match self.auth_service.authenticate_credentials(&username, &password).await {
            Ok(session_token) => {
                info!("Client authenticated successfully: {}", username);
                Ok(Response::new(AuthenticateResponse {
                    success: true,
                    session_token,
                    message: "Authentication successful".to_string(),
                }))
            },
            Err(status) => {
                warn!("Authentication failed for user: {}", username);
                Ok(Response::new(AuthenticateResponse {
                    success: false,
                    session_token: String::new(),
                    message: status.message().to_string(),
                }))
            }
        }
    }

    // Validate authentication for requests (if auth is enabled)
    // Optimized to have zero overhead when auth is disabled
    async fn validate_request_auth<T>(&self, request: &Request<T>) -> Result<(), Status> {
        // Fast path: Skip all processing if auth is disabled
        // This check is synchronous and has minimal overhead
        if !self.auth_service.is_auth_enabled() {
            return Ok(());
        }

        // Auth is enabled, proceed with optimized validation
        // Fast metadata access - try most common header first
        let metadata = request.metadata();

        let token = match metadata.get("authorization") {
            Some(header_value) => {
                // Fast path: Convert MetadataValue to string slice without allocation
                match std::str::from_utf8(header_value.as_bytes()) {
                    Ok(header_str) => {
                        // Optimized Bearer token extraction
                        if header_str.len() > 7 && header_str.starts_with("Bearer ") {
                            &header_str[7..] // Remove "Bearer " prefix
                        } else if header_str.len() > 7 && header_str.starts_with("bearer ") {
                            &header_str[7..] // Handle lowercase
                        } else {
                            // Assume the entire header is the token
                            header_str
                        }
                    }
                    Err(_) => {
                        return Err(Status::unauthenticated("Invalid authorization header encoding"));
                    }
                }
            }
            None => {
                // Fallback: try alternative header names (less common)
                match metadata.get("Authorization").or_else(|| metadata.get("AUTHORIZATION")) {
                    Some(header_value) => {
                        match std::str::from_utf8(header_value.as_bytes()) {
                            Ok(header_str) => {
                                if header_str.len() > 7 && header_str.starts_with("Bearer ") {
                                    &header_str[7..]
                                } else {
                                    header_str
                                }
                            }
                            Err(_) => {
                                return Err(Status::unauthenticated("Invalid authorization header encoding"));
                            }
                        }
                    }
                    None => {
                        return Err(Status::unauthenticated("Missing authorization header"));
                    }
                }
            }
        };

        // Validate the session token
        self.auth_service.validate_session(token).await?;
        Ok(())
    }

    // Ping Redis to check connectivity
    async fn ping_internal(&self) -> Result<Response<PingResponse>, Status> {
        let start_time = std::time::Instant::now();

        match self.redis_client.ping().await {
            Ok(_) => {
                let latency = start_time.elapsed().as_millis() as i64;
                Ok(Response::new(PingResponse {
                    success: true,
                    message: "PONG".to_string(),
                    latency_ms: latency,
                }))
            },
            Err(e) => {
                error!("Failed to ping Redis: {}", e);
                Err(Status::internal(format!("Failed to ping Redis: {}", e)))
            }
        }
    }

    // Ping with authentication validation
    async fn ping(&self, request: Request<PingRequest>) -> Result<Response<PingResponse>, Status> {
        // Fast path: Skip auth validation if disabled (zero overhead)
        if self.auth_service.is_auth_enabled() {
            if let Err(status) = self.validate_request_auth(&request).await {
                return Err(status);
            }
        }

        self.ping_internal().await
    }

    // Start a background task to periodically check node health
    fn start_node_health_check_task(&self) {
        let service_clone = self.clone();
        tokio::spawn(async move {
            let mut check_interval = tokio::time::interval(Duration::from_secs(30)); // Check every 30 seconds

            loop {
                check_interval.tick().await;

                // Get a list of all nodes
                let nodes = service_clone.secondary_nodes.clone();

                // Check each node in parallel
                let mut futures = Vec::with_capacity(nodes.len());
                for node in nodes {
                    let service_clone = service_clone.clone();
                    let node_clone = node.clone();

                    futures.push(tokio::spawn(async move {
                        let mut node_pool = service_clone.node_pool.write().await;

                        // Try to get a client for the node
                        let is_available = match node_pool.get_client(&node_clone).await {
                            Some(_) => true,
                            None => false,
                        };

                        // Update node availability
                        let old_status = node_pool.node_availability.get(&node_clone).cloned().unwrap_or(false);
                        if old_status != is_available {
                            node_pool.node_availability.insert(node_clone.clone(), is_available);
                            if is_available {
                                info!("Node {} is now available", node_clone);
                            } else {
                                warn!("Node {} is now unavailable", node_clone);
                            }
                        }

                        // Update the last check timestamp
                        node_pool.last_availability_check = std::time::Instant::now();
                    }));
                }

                // Wait for all checks to complete
                join_all(futures).await;
            }
        });
    }

    pub async fn new(
        redis_client: RedisClient,
        secondary_nodes: Vec<String>,
        replication_factor: usize,
        read_consistency: usize,
        async_replication: bool,
        max_retries: u32,
        retry_delay_ms: u64,
        replication_batch_max_age_secs: u64,
        pool_size: usize,
        max_batch_size: usize,
        flush_interval_ms: u64,
        chunk_size: usize,
        num_shards: usize,
        auth_service: Arc<AuthService>,
        // Write consistency parameters
        peer_redis_nodes: Vec<String>,
        write_consistency: Option<WriteConsistency>,
        quorum_value: usize,
        write_retry_count: u32,
        peer_redis_pool_size: usize,
        // Site replication parameters
        site_replication_enabled: bool,
        site_primary_node: String,
        site_failover_node: String,
        site_replication_retry_count: u32,
        site_replication_timeout_ms: u64,
        site_replication_pool_size: usize,
        use_physical_connections: bool,
    ) -> Self {
        // Only create secondary node pool if async replication is enabled
        let node_pool = if async_replication && replication_factor > 0 && !secondary_nodes.is_empty() {
            info!("Creating secondary node pool for async replication with {} nodes", secondary_nodes.len());
            Arc::new(RwLock::new(
                SecondaryNodePool::new(
                    &secondary_nodes,
                    max_retries,
                    Duration::from_millis(retry_delay_ms),
                    pool_size,
                    use_physical_connections,
                ).await
            ))
        } else {
            info!("Skipping secondary node pool creation (async_replication={}, replication_factor={}, secondary_nodes={})",
                  async_replication, replication_factor, secondary_nodes.len());
            // Create empty pool for synchronous replication mode
            Arc::new(RwLock::new(
                SecondaryNodePool::new(
                    &[],  // Empty nodes list
                    max_retries,
                    Duration::from_millis(retry_delay_ms),
                    pool_size,
                    use_physical_connections,
                ).await
            ))
        };

        let batch_collectors = Arc::new(ShardedBatchCollectors::new(num_shards));

        // Create site replication batch collector and channel
        let site_replication_collector = Arc::new(tokio::sync::Mutex::new(
            SiteReplicationBatchCollector::new(max_batch_size, Duration::from_millis(flush_interval_ms))
        ));

        let (site_replication_sender, mut site_replication_receiver) = if site_replication_enabled {
            let (tx, rx) = mpsc::channel::<WriteOperation>(max_batch_size * 8);
            (Some(tx), Some(rx))
        } else {
            (None, None)
        };
        let batch_flush_task_running = Arc::new(AtomicUsize::new(0));
        let flush_interval = Duration::from_millis(flush_interval_ms);

        // Initialize write consistency checker if peer Redis nodes are configured
        let write_consistency_checker = if !peer_redis_nodes.is_empty() && write_consistency.is_some() {
            info!("Initializing write consistency checker with user-configured pool size {} per peer node", peer_redis_pool_size);

            let checker = WriteConsistencyChecker::new(
                &peer_redis_nodes,
                write_consistency.unwrap(),
                quorum_value,
                peer_redis_pool_size,
                write_retry_count,
            ).await;
            Some(Arc::new(checker))
        } else {
            None
        };

        // Create site replication manager if needed
        let site_replication_manager = if site_replication_enabled && (!site_primary_node.is_empty() || !site_failover_node.is_empty()) {
            info!("Creating site replication manager with primary node: {} and failover node: {} and pool size: {}",
                  site_primary_node, site_failover_node, site_replication_pool_size);
            let manager = SiteReplicationManager::new(
                site_primary_node.clone(),
                site_failover_node.clone(),
                site_replication_pool_size,
                site_replication_retry_count,
                site_replication_timeout_ms,
            ).await;
            Some(Arc::new(tokio::sync::Mutex::new(manager)))
        } else {
            None
        };

        let mut op_senders = HashMap::new();

        // --- Construct the service first ---
        let service = Self {
            redis_client,
            secondary_nodes: secondary_nodes.clone(),
            replication_factor,
            read_consistency,
            async_replication,
            node_pool: node_pool.clone(),
            max_retries,
            retry_delay: Duration::from_millis(retry_delay_ms),
            replication_batch_max_age: Duration::from_secs(replication_batch_max_age_secs),
            batch_collectors,
            max_batch_size,
            flush_interval,
            batch_flush_task_running,
            op_senders: Arc::new(HashMap::new()), // temporary, will be replaced below
            chunk_size,
            num_shards,
            auth_service: auth_service.clone(),
            write_consistency_checker,
            peer_redis_nodes: peer_redis_nodes.clone(),
            site_replication_manager: site_replication_manager.clone(),
            site_replication_enabled,
            site_replication_collector: site_replication_collector.clone(),
            site_replication_sender: site_replication_sender.clone(),
        };

        // Only set up background tasks and channels if async replication is enabled
        if async_replication && replication_factor > 0 && !secondary_nodes.is_empty() {
            info!("Setting up background replication tasks for {} secondary nodes", secondary_nodes.len());

            // Get available nodes from the node pool
            let available_nodes = {
                let node_pool_guard = node_pool.read().await;
                node_pool_guard.get_available_nodes(&secondary_nodes, replication_factor)
            };

            // Now set up per-node channels and background tasks
            for node in available_nodes {
                // Increase buffer size for better throughput and to reduce backpressure
                let (tx, mut rx) = mpsc::channel::<ReplicationOperation>(max_batch_size * 8);
                op_senders.insert(node.clone(), tx);

                let service_clone = service.clone();
                let node_clone = node.clone();
                tokio::spawn(async move {
                    let mut collector = BatchCollector::new(max_batch_size, flush_interval);
                    loop {
                        tokio::select! {
                            Some(op) = rx.recv() => {
                                if collector.add_operation(op) {
                                    let batch = collector.take_batch();
                                    service_clone.flush_node_batch_with_ops(&node_clone, batch).await;
                                }
                            }
                            _ = tokio::time::sleep(flush_interval) => {
                                if !collector.is_empty() {
                                    let batch = collector.take_batch();
                                    service_clone.flush_node_batch_with_ops(&node_clone, batch).await;
                                }
                            }
                        }
                    }
                });
            }
        } else {
            info!("Skipping background replication task setup (async_replication={}, replication_factor={}, secondary_nodes={})",
                  async_replication, replication_factor, secondary_nodes.len());
        }

        // Set up site replication background task if enabled
        if site_replication_enabled && site_replication_receiver.is_some() {
            let mut receiver = site_replication_receiver.unwrap();
            let collector = site_replication_collector.clone();
            let site_manager = site_replication_manager.clone();
            let flush_interval = Duration::from_millis(flush_interval_ms);

            info!("Starting site replication background task");
            tokio::spawn(async move {
                let mut collector_guard = collector.lock().await;
                loop {
                    tokio::select! {
                        Some(operation) = receiver.recv() => {
                            if collector_guard.add_operation(operation) {
                                let batch = collector_guard.take_batch();
                                drop(collector_guard); // Release lock before async operation

                                if let Some(manager) = &site_manager {
                                    let mut manager_guard = manager.lock().await;
                                    let success = manager_guard.replicate_to_sites(batch).await;
                                    if !success {
                                        warn!("Site replication batch failed");
                                    }
                                }

                                collector_guard = collector.lock().await; // Re-acquire lock
                            }
                        }
                        _ = tokio::time::sleep(flush_interval) => {
                            if !collector_guard.is_empty() {
                                let batch = collector_guard.take_batch();
                                drop(collector_guard); // Release lock before async operation

                                if let Some(manager) = &site_manager {
                                    let mut manager_guard = manager.lock().await;
                                    let success = manager_guard.replicate_to_sites(batch).await;
                                    if !success {
                                        warn!("Site replication batch failed");
                                    }
                                }

                                collector_guard = collector.lock().await; // Re-acquire lock
                            }
                        }
                    }
                }
            });
        } else {
            info!("Skipping site replication background task (site_replication_enabled={})", site_replication_enabled);
        }

        // Create the service with the correct op_senders
        let service = Self {
            op_senders: Arc::new(op_senders),
            auth_service,
            write_consistency_checker: service.write_consistency_checker.clone(),
            peer_redis_nodes: service.peer_redis_nodes.clone(),
            site_replication_manager: service.site_replication_manager.clone(),
            site_replication_enabled: service.site_replication_enabled,
            site_replication_collector: service.site_replication_collector.clone(),
            site_replication_sender: service.site_replication_sender.clone(),
            ..service
        };

        // Only start health check task if async replication is enabled
        if async_replication && replication_factor > 0 && !secondary_nodes.is_empty() {
            info!("Starting node health check task for secondary nodes");
            service.start_node_health_check_task();
        } else {
            info!("Skipping node health check task (async_replication={}, replication_factor={}, secondary_nodes={})",
                  async_replication, replication_factor, secondary_nodes.len());
        }

        service
    }

    pub fn into_server(self) -> KeyValueServiceServer<Self> {
        KeyValueServiceServer::new(self)
    }

    /// Execute write operation with consistency checking for synchronous replication
    async fn execute_write_with_consistency(&self, operation: WriteOperation) -> bool {
        // If async replication is enabled or no write consistency checker, skip consistency check
        if self.async_replication || self.write_consistency_checker.is_none() {
            return true;
        }

        // If no peer Redis nodes configured, skip consistency check
        if self.peer_redis_nodes.is_empty() {
            return true;
        }

        // Execute write operation on peer Redis nodes with consistency checking
        if let Some(checker) = &self.write_consistency_checker {
            checker.execute_write_with_consistency(&self.peer_redis_nodes, operation).await
        } else {
            true
        }
    }

    /// Add operation to site replication batch
    async fn add_operation_to_site_replication(&self, operation: WriteOperation) {
        // Skip if site replication is disabled or no sender
        if !self.site_replication_enabled || self.site_replication_sender.is_none() {
            return;
        }

        if let Some(sender) = &self.site_replication_sender {
            if let Err(e) = sender.send(operation).await {
                warn!("Failed to send operation to site replication batch: {}", e);
            }
        }
    }

    /// Execute site replication for cross-site data replication (legacy method for backward compatibility)
    async fn execute_site_replication(&self, operations: Vec<WriteOperation>) -> bool {
        // Skip if site replication is disabled, async replication is disabled, or no operations
        if !self.site_replication_enabled || !self.async_replication || operations.is_empty() {
            return true;
        }

        // For backward compatibility, send each operation to the batch
        for operation in operations {
            self.add_operation_to_site_replication(operation).await;
        }

        true // Always return true since batching is async
    }

    /// Replicate a batch of operations to secondary nodes
    ///
    /// Optimized for high throughput with adaptive concurrency and chunking
    async fn replicate_batch(&self, batch: ReplicationBatch) {
        // Skip replication if there are no operations or no secondary nodes
        if batch.operations.is_empty() || self.replication_factor == 0 {
            return;
        }

        // Use configured chunk size instead of constant
        // Pre-allocate the chunks vector to avoid reallocations
        let operations = &batch.operations;

        // Adaptive chunk size based on batch size
        let adaptive_chunk_size = if operations.len() > 50000 {
            self.chunk_size * 2
        } else if operations.len() > 20000 {
            self.chunk_size * 3 / 2
        } else {
            self.chunk_size
        };

        // Create chunks with the adaptive size
        let chunks: Vec<&[ReplicationOperation]> = operations.chunks(adaptive_chunk_size).collect();

        // Adaptive concurrency based on number of chunks
        // Use more concurrency for fewer chunks, less for many chunks
        let max_concurrent_chunks = if chunks.len() <= 4 {
            16  // High concurrency for small number of chunks
        } else if chunks.len() <= 16 {
            12  // Medium concurrency for medium number of chunks
        } else {
            8   // Lower concurrency for many chunks to avoid overwhelming the system
        };

        // Process chunks in batches with adaptive concurrency
        for chunks_batch in chunks.chunks(max_concurrent_chunks) {
            let mut futures = Vec::with_capacity(chunks_batch.len());

            for chunk in chunks_batch {
                let self_clone = self.clone();
                // Use a reference where possible to avoid cloning
                // Only clone when we need to move into an async task
                let chunk_vec = chunk.to_vec();
                let batch = ReplicationBatch::new(chunk_vec, self.replication_batch_max_age);

                futures.push(tokio::spawn(async move {
                    self_clone.replicate_chunk(batch).await;
                }));
            }

            // Wait for this batch of chunks to complete before processing the next batch
            // This provides backpressure and prevents overwhelming the system
            join_all(futures).await;
        }
    }

    /// Helper method to replicate a single chunk to all target nodes in parallel
    ///
    /// Optimized for minimal locking and maximum parallelism
    async fn replicate_chunk(&self, batch: ReplicationBatch) {
        // Skip if batch is already expired to avoid wasted work
        if batch.is_expired() {
            debug!("Skipping expired batch with {} operations", batch.operations.len());
            return;
        }

        // Pre-allocate with exact capacity
        let mut node_clients = Vec::with_capacity(self.replication_factor);

        // Use a simpler approach with a single write lock
        // This is more reliable and avoids potential issues with concurrent access
        {
            // Acquire write lock once
            let mut node_pool = self.node_pool.write().await;

            // Get available nodes
            let available_nodes = node_pool.get_available_nodes(&self.secondary_nodes, self.replication_factor);

            // Get clients for all available nodes
            for node in available_nodes {
                if let Some(client) = node_pool.get_client(&node).await {
                    node_clients.push((node.clone(), client));
                } else {
                    error!("Failed to get client for node {}", node);
                }
            }
        } // Release the lock here

        // If we have no clients, there's nothing to do
        if node_clients.is_empty() {
            return;
        }

        // Process all nodes in parallel for maximum throughput
        // This is more efficient for small numbers of nodes (like 2-3)
        let mut futures = Vec::with_capacity(node_clients.len());

        for (node, client) in node_clients {
            let self_clone = self.clone();
            let batch_clone = batch.clone();

            // Avoid unnecessary cloning by moving the node string directly
            futures.push(tokio::spawn(async move {
                self_clone.send_batch_to_client(&node, batch_clone, client).await;
            }));
        }

        // Wait for all nodes to complete in parallel
        // This ensures we don't return until all replication is complete or failed
        join_all(futures).await;
    }



    async fn set(&self, request: Request<SetRequest>) -> Result<Response<SetResponse>, Status> {
        // Check if this is a site replication request (skip_site_replication = true)
        let is_site_replication = request.get_ref().skip_site_replication;

        // Skip auth validation if this is a site replication request or auth is disabled
        if self.auth_service.is_auth_enabled() && !is_site_replication {
            if let Err(status) = self.validate_request_auth(&request).await {
                return Err(status);
            }
        }

        let SetRequest { key, value, skip_replication, skip_site_replication } = request.into_inner();

        match self.redis_client.set(&key, &value).await {
            Ok(_) => {
                if skip_replication || self.replication_factor == 0 {
                    return Ok(Response::new(SetResponse { success: true }));
                }

                // Create a replication operation
                let operation = ReplicationOperation {
                    command: "set".to_string(),
                    key: key.clone(),
                    value: value.clone(),
                    field: None,
                };

                // Add to batch or replicate immediately based on async setting
                if self.async_replication {
                    // Add to batch for async replication in a background task
                    // This ensures we don't block the client response at all
                    let self_clone = self.clone();
                    let operation_clone = operation.clone();
                    tokio::spawn(async move {
                        self_clone.add_operation_to_batches(operation_clone).await;
                    });

                    // Execute site replication if enabled (only for async replication) and not skipped
                    if self.site_replication_enabled && !skip_site_replication {
                        let write_op = WriteOperation::Set { key: key.clone(), value: value.clone() };
                        let self_clone = self.clone();
                        tokio::spawn(async move {
                            self_clone.add_operation_to_site_replication(write_op).await;
                        });
                    }

                    Ok(Response::new(SetResponse { success: true }))
                } else {
                    // For synchronous replication, check write consistency with peer Redis nodes
                    let write_op = WriteOperation::Set { key: key.clone(), value: value.clone() };
                    let consistency_success = self.execute_write_with_consistency(write_op).await;

                    if !consistency_success {
                        error!("Write consistency failed for SET operation on key: {}", key);
                        return Err(Status::internal("Write consistency failed"));
                    }

                    // When async_replication = false, we only do write consistency to peer Redis nodes
                    // No replication to secondary Rust nodes and no site replication
                    Ok(Response::new(SetResponse { success: true }))
                }
            }
            Err(e) => {
                error!("Failed to set key: {}", e);
                Err(Status::internal("Failed to set key"))
            }
        }
    }

    async fn get(&self, request: Request<GetRequest>) -> Result<Response<GetResponse>, Status> {
        // Fast path: Skip auth validation if disabled (zero overhead)
        if self.auth_service.is_auth_enabled() {
            if let Err(status) = self.validate_request_auth(&request).await {
                return Err(status);
            }
        }

        let GetRequest { key } = request.into_inner();

        match self.redis_client.get(&key).await {
            Ok(value) => Ok(Response::new(GetResponse {
                value,
                found: true,
            })),
            Err(e) => {
                error!("Failed to get key: {}", e);
                Err(Status::internal("Failed to get key"))
            }
        }
    }

    async fn delete(&self, request: Request<DeleteRequest>) -> Result<Response<DeleteResponse>, Status> {
        // Check if this is a site replication request (skip_site_replication = true)
        let is_site_replication = request.get_ref().skip_site_replication;

        // Skip auth validation if this is a site replication request or auth is disabled
        if self.auth_service.is_auth_enabled() && !is_site_replication {
            if let Err(status) = self.validate_request_auth(&request).await {
                return Err(status);
            }
        }

        let DeleteRequest { key, skip_replication, skip_site_replication } = request.into_inner();

        match self.redis_client.del(&key).await {
            Ok(_) => {
                if skip_replication || self.replication_factor == 0 {
                    return Ok(Response::new(DeleteResponse { success: true }));
                }

                // Create a replication operation
                let operation = ReplicationOperation {
                    command: "del".to_string(),
                    key: key.clone(),
                    value: String::new(),
                    field: None,
                };

                // Add to batch or replicate immediately based on async setting
                if self.async_replication {
                    // Add to batch for async replication in a background task
                    // This ensures we don't block the client response at all
                    let self_clone = self.clone();
                    let operation_clone = operation.clone();
                    tokio::spawn(async move {
                        self_clone.add_operation_to_batches(operation_clone).await;
                    });

                    // Execute site replication if enabled (only for async replication) and not skipped
                    if self.site_replication_enabled && !skip_site_replication {
                        let write_op = WriteOperation::Delete { key: key.clone() };
                        let self_clone = self.clone();
                        tokio::spawn(async move {
                            self_clone.add_operation_to_site_replication(write_op).await;
                        });
                    }

                    Ok(Response::new(DeleteResponse { success: true }))
                } else {
                    // For synchronous replication, check write consistency with peer Redis nodes
                    let write_op = WriteOperation::Delete { key: key.clone() };
                    let consistency_success = self.execute_write_with_consistency(write_op).await;

                    if !consistency_success {
                        error!("Write consistency failed for DELETE operation on key: {}", key);
                        return Err(Status::internal("Write consistency failed"));
                    }

                    // When async_replication = false, we only do write consistency to peer Redis nodes
                    // No replication to secondary Rust nodes to ensure immediate client response
                    Ok(Response::new(DeleteResponse { success: true }))
                }
            }
            Err(e) => {
                error!("Failed to delete key: {}", e);
                Err(Status::internal("Failed to delete key"))
            }
        }
    }

    async fn set_ex(&self, request: Request<SetExRequest>) -> Result<Response<SetExResponse>, Status> {
        // Check if this is a site replication request (skip_site_replication = true)
        let is_site_replication = request.get_ref().skip_site_replication;

        // Skip auth validation if this is a site replication request or auth is disabled
        if self.auth_service.is_auth_enabled() && !is_site_replication {
            if let Err(status) = self.validate_request_auth(&request).await {
                return Err(status);
            }
        }

        let SetExRequest { key, value, ttl, skip_replication, skip_site_replication } = request.into_inner();

        match self.redis_client.setex(&key, &value, ttl).await {
            Ok(_) => {
                if skip_replication || self.replication_factor == 0 {
                    return Ok(Response::new(SetExResponse { success: true }));
                }

                // Create a replication operation
                let operation = ReplicationOperation {
                    command: "setex".to_string(),
                    key: key.clone(),
                    value: value.clone(),
                    field: None,
                };

                // Add to batch or replicate immediately based on async setting
                if self.async_replication {
                    // Add to batch for async replication in a background task
                    // This ensures we don't block the client response at all
                    let self_clone = self.clone();
                    let operation_clone = operation.clone();
                    tokio::spawn(async move {
                        self_clone.add_operation_to_batches(operation_clone).await;
                    });

                    // Execute site replication if enabled (only for async replication) and not skipped
                    if self.site_replication_enabled && !skip_site_replication {
                        let write_op = WriteOperation::SetEx { key: key.clone(), value: value.clone(), ttl };
                        let self_clone = self.clone();
                        tokio::spawn(async move {
                            self_clone.add_operation_to_site_replication(write_op).await;
                        });
                    }

                    Ok(Response::new(SetExResponse { success: true }))
                } else {
                    // For synchronous replication, check write consistency with peer Redis nodes
                    let write_op = WriteOperation::SetEx { key: key.clone(), value: value.clone(), ttl };
                    let consistency_success = self.execute_write_with_consistency(write_op).await;

                    if !consistency_success {
                        error!("Write consistency failed for SETEX operation on key: {}", key);
                        return Err(Status::internal("Write consistency failed"));
                    }

                    // When async_replication = false, we only do write consistency to peer Redis nodes
                    // No replication to secondary Rust nodes to ensure immediate client response
                    Ok(Response::new(SetExResponse { success: true }))
                }
            }
            Err(e) => {
                error!("Failed to set key with expiry: {}", e);
                Err(Status::internal("Failed to set key with expiry"))
            }
        }
    }

    async fn set_expiry(&self, request: Request<SetExpiryRequest>) -> Result<Response<SetExpiryResponse>, Status> {
        // Check if this is a site replication request (skip_site_replication = true)
        let is_site_replication = request.get_ref().skip_site_replication;

        // Skip auth validation if this is a site replication request or auth is disabled
        if self.auth_service.is_auth_enabled() && !is_site_replication {
            if let Err(status) = self.validate_request_auth(&request).await {
                return Err(status);
            }
        }

        let SetExpiryRequest { key, ttl, skip_replication, skip_site_replication: _ } = request.into_inner();

        match self.redis_client.set_expiry(&key, ttl).await {
            Ok(_) => {
                if skip_replication || self.replication_factor == 0 {
                    return Ok(Response::new(SetExpiryResponse { success: true }));
                }

                // Create a replication operation
                let operation = ReplicationOperation {
                    command: "setexpiry".to_string(),
                    key: key.clone(),
                    value: ttl.to_string(),
                    field: None,
                };

                // Add to batch or replicate immediately based on async setting
                if self.async_replication {
                    // Add to batch for async replication in a background task
                    // This ensures we don't block the client response at all
                    let self_clone = self.clone();
                    let operation_clone = operation.clone();
                    tokio::spawn(async move {
                        self_clone.add_operation_to_batches(operation_clone).await;
                    });

                    // Execute site replication if enabled (only for async replication) and not skipped
                    if self.site_replication_enabled && !is_site_replication {
                        let write_op = WriteOperation::SetExpiry { key: key.clone(), ttl };
                        let self_clone = self.clone();
                        tokio::spawn(async move {
                            self_clone.add_operation_to_site_replication(write_op).await;
                        });
                    }

                    Ok(Response::new(SetExpiryResponse { success: true }))
                } else {
                    // For synchronous replication, check write consistency with peer Redis nodes
                    let write_op = WriteOperation::SetExpiry { key: key.clone(), ttl };
                    let consistency_success = self.execute_write_with_consistency(write_op).await;

                    if !consistency_success {
                        error!("Write consistency failed for SET_EXPIRY operation on key: {}", key);
                        return Err(Status::internal("Write consistency failed"));
                    }

                    // When async_replication = false, we only do write consistency to peer Redis nodes
                    // No replication to secondary Rust nodes to ensure immediate client response
                    Ok(Response::new(SetExpiryResponse { success: true }))
                }
            }
            Err(e) => {
                error!("Failed to set key expiry: {}", e);
                Err(Status::internal("Failed to set key expiry"))
            }
        }
    }

    async fn incr_by(&self, request: Request<IncrByRequest>) -> Result<Response<IncrByResponse>, Status> {
        // Check if this is a site replication request (skip_site_replication = true)
        let is_site_replication = request.get_ref().skip_site_replication;

        // Skip auth validation if this is a site replication request or auth is disabled
        if self.auth_service.is_auth_enabled() && !is_site_replication {
            if let Err(status) = self.validate_request_auth(&request).await {
                return Err(status);
            }
        }

        let IncrByRequest { key, value, skip_replication, skip_site_replication: _ } = request.into_inner();

        match self.redis_client.incr(&key, value).await {
            Ok(new_value) => {
                if skip_replication || self.replication_factor == 0 {
                    return Ok(Response::new(IncrByResponse { new_value }));
                }

                // Create a replication operation
                let operation = ReplicationOperation {
                    command: "incrby".to_string(),
                    key: key.clone(),
                    value: value.to_string(),
                    field: None,
                };

                // Add to batch or replicate immediately based on async setting
                if self.async_replication {
                    // Add to batch for async replication in a background task
                    // This ensures we don't block the client response at all
                    let self_clone = self.clone();
                    let operation_clone = operation.clone();
                    tokio::spawn(async move {
                        self_clone.add_operation_to_batches(operation_clone).await;
                    });

                    // Execute site replication if enabled (only for async replication) and not skipped
                    if self.site_replication_enabled && !is_site_replication {
                        let write_op = WriteOperation::IncrBy { key: key.clone(), value };
                        let self_clone = self.clone();
                        tokio::spawn(async move {
                            self_clone.add_operation_to_site_replication(write_op).await;
                        });
                    }

                    Ok(Response::new(IncrByResponse { new_value }))
                } else {
                    // For synchronous replication, check write consistency with peer Redis nodes
                    let write_op = WriteOperation::IncrBy { key: key.clone(), value };
                    let consistency_success = self.execute_write_with_consistency(write_op).await;

                    if !consistency_success {
                        error!("Write consistency failed for INCRBY operation on key: {}", key);
                        return Err(Status::internal("Write consistency failed"));
                    }

                    // When async_replication = false, we only do write consistency to peer Redis nodes
                    // No replication to secondary Rust nodes to ensure immediate client response
                    Ok(Response::new(IncrByResponse { new_value }))
                }
            }
            Err(e) => {
                error!("Failed to increment key: {}", e);
                Err(Status::internal("Failed to increment key"))
            }
        }
    }

    async fn decr_by(&self, request: Request<DecrByRequest>) -> Result<Response<DecrByResponse>, Status> {
        // Check if this is a site replication request (skip_site_replication = true)
        let is_site_replication = request.get_ref().skip_site_replication;

        // Skip auth validation if this is a site replication request or auth is disabled
        if self.auth_service.is_auth_enabled() && !is_site_replication {
            if let Err(status) = self.validate_request_auth(&request).await {
                return Err(status);
            }
        }

        let DecrByRequest { key, value, skip_replication, skip_site_replication: _ } = request.into_inner();

        match self.redis_client.decr(&key, value).await {
            Ok(new_value) => {
                if skip_replication || self.replication_factor == 0 {
                    return Ok(Response::new(DecrByResponse { new_value }));
                }

                // Create a replication operation
                let operation = ReplicationOperation {
                    command: "decrby".to_string(),
                    key: key.clone(),
                    value: value.to_string(),
                    field: None,
                };

                // Add to batch or replicate immediately based on async setting
                if self.async_replication {
                    // Add to batch for async replication in a background task
                    // This ensures we don't block the client response at all
                    let self_clone = self.clone();
                    let operation_clone = operation.clone();
                    tokio::spawn(async move {
                        self_clone.add_operation_to_batches(operation_clone).await;
                    });

                    // Execute site replication if enabled (only for async replication) and not skipped
                    if self.site_replication_enabled && !is_site_replication {
                        let write_op = WriteOperation::DecrBy { key: key.clone(), value };
                        let self_clone = self.clone();
                        tokio::spawn(async move {
                            self_clone.add_operation_to_site_replication(write_op).await;
                        });
                    }

                    Ok(Response::new(DecrByResponse { new_value }))
                } else {
                    // For synchronous replication, check write consistency with peer Redis nodes
                    let write_op = WriteOperation::DecrBy { key: key.clone(), value };
                    let consistency_success = self.execute_write_with_consistency(write_op).await;

                    if !consistency_success {
                        error!("Write consistency failed for DECRBY operation on key: {}", key);
                        return Err(Status::internal("Write consistency failed"));
                    }

                    // When async_replication = false, we only do write consistency to peer Redis nodes
                    // No replication to secondary Rust nodes to ensure immediate client response
                    Ok(Response::new(DecrByResponse { new_value }))
                }
            }
            Err(e) => {
                error!("Failed to decrement key: {}", e);
                Err(Status::internal("Failed to decrement key"))
            }
        }
    }

    async fn incr_by_float(&self, request: Request<IncrByFloatRequest>) -> Result<Response<IncrByFloatResponse>, Status> {
        // Check if this is a site replication request (skip_site_replication = true)
        let is_site_replication = request.get_ref().skip_site_replication;

        // Skip auth validation if this is a site replication request or auth is disabled
        if self.auth_service.is_auth_enabled() && !is_site_replication {
            if let Err(status) = self.validate_request_auth(&request).await {
                return Err(status);
            }
        }

        let IncrByFloatRequest { key, value, skip_replication, skip_site_replication: _ } = request.into_inner();

        match self.redis_client.incrbyfloat(&key, value).await {
            Ok(new_value) => {
                if skip_replication || self.replication_factor == 0 {
                    return Ok(Response::new(IncrByFloatResponse { new_value }));
                }

                // Create a replication operation
                let operation = ReplicationOperation {
                    command: "incrbyfloat".to_string(),
                    key: key.clone(),
                    value: value.to_string(),
                    field: None,
                };

                // Add to batch or replicate immediately based on async setting
                if self.async_replication {
                    // Add to batch for async replication in a background task
                    self.replicate_async_in_background(operation);

                    // Execute site replication if enabled (only for async replication) and not skipped
                    if self.site_replication_enabled && !is_site_replication {
                        let write_op = WriteOperation::IncrByFloat { key: key.clone(), value };
                        let self_clone = self.clone();
                        tokio::spawn(async move {
                            self_clone.add_operation_to_site_replication(write_op).await;
                        });
                    }

                    Ok(Response::new(IncrByFloatResponse { new_value }))
                } else {
                    // For synchronous replication, check write consistency with peer Redis nodes
                    let write_op = WriteOperation::IncrByFloat { key: key.clone(), value };
                    let consistency_success = self.execute_write_with_consistency(write_op).await;

                    if !consistency_success {
                        error!("Write consistency failed for INCRBYFLOAT operation on key: {}", key);
                        return Err(Status::internal("Write consistency failed"));
                    }

                    // When async_replication = false, we only do write consistency to peer Redis nodes
                    // No replication to secondary Rust nodes to ensure immediate client response
                    Ok(Response::new(IncrByFloatResponse { new_value }))
                }
            }
            Err(e) => {
                error!("Failed to increment float key: {}", e);
                Err(Status::internal("Failed to increment float key"))
            }
        }
    }

    async fn h_set(&self, request: Request<HSetRequest>) -> Result<Response<HSetResponse>, Status> {
        // Check if this is a site replication request (skip_site_replication = true)
        let is_site_replication = request.get_ref().skip_site_replication;

        // Skip auth validation if this is a site replication request or auth is disabled
        if self.auth_service.is_auth_enabled() && !is_site_replication {
            if let Err(status) = self.validate_request_auth(&request).await {
                return Err(status);
            }
        }

        let HSetRequest { key, field, value, skip_replication, skip_site_replication: _ } = request.into_inner();

        match self.redis_client.hset(&key, &field, &value).await {
            Ok(_) => {
                if skip_replication || self.replication_factor == 0 {
                    return Ok(Response::new(HSetResponse { success: true }));
                }

                // Create a replication operation
                let operation = ReplicationOperation {
                    command: "hset".to_string(),
                    key: key.clone(),
                    value: value.clone(),
                    field: Some(field.clone()),
                };

                // Add to batch or replicate immediately based on async setting
                if self.async_replication {
                    // Add to batch for async replication in a background task
                    self.replicate_async_in_background(operation);

                    // Execute site replication if enabled (only for async replication) and not skipped
                    if self.site_replication_enabled && !is_site_replication {
                        let write_op = WriteOperation::HSet { key: key.clone(), field: field.clone(), value: value.clone() };
                        let self_clone = self.clone();
                        tokio::spawn(async move {
                            self_clone.add_operation_to_site_replication(write_op).await;
                        });
                    }

                    Ok(Response::new(HSetResponse { success: true }))
                } else {
                    // For synchronous replication, check write consistency with peer Redis nodes
                    let write_op = WriteOperation::HSet { key: key.clone(), field: field.clone(), value: value.clone() };
                    let consistency_success = self.execute_write_with_consistency(write_op).await;

                    if !consistency_success {
                        error!("Write consistency failed for HSET operation on key: {}", key);
                        return Err(Status::internal("Write consistency failed"));
                    }

                    // When async_replication = false, we only do write consistency to peer Redis nodes
                    // No replication to secondary Rust nodes to ensure immediate client response
                    Ok(Response::new(HSetResponse { success: true }))
                }
            }
            Err(e) => {
                error!("Failed to set hash field: {}", e);
                Err(Status::internal("Failed to set hash field"))
            }
        }
    }

    async fn h_get(&self, request: Request<HGetRequest>) -> Result<Response<HGetResponse>, Status> {
        // Fast path: Skip auth validation if disabled (zero overhead)
        if self.auth_service.is_auth_enabled() {
            if let Err(status) = self.validate_request_auth(&request).await {
                return Err(status);
            }
        }

        let HGetRequest { key, field } = request.into_inner();

        match self.redis_client.hget(&key, &field).await {
            Ok(value) => Ok(Response::new(HGetResponse {
                value,
                found: true,
            })),
            Err(e) => {
                error!("Failed to get hash field: {}", e);
                Err(Status::internal("Failed to get hash field"))
            }
        }
    }

    async fn h_get_all(&self, request: Request<HGetAllRequest>) -> Result<Response<HGetAllResponse>, Status> {
        // Fast path: Skip auth validation if disabled (zero overhead)
        if self.auth_service.is_auth_enabled() {
            if let Err(status) = self.validate_request_auth(&request).await {
                return Err(status);
            }
        }

        let HGetAllRequest { key } = request.into_inner();

        match self.redis_client.hgetall(&key).await {
            Ok(fields) => Ok(Response::new(HGetAllResponse { fields })),
            Err(e) => {
                error!("Failed to get all hash fields: {}", e);
                Err(Status::internal("Failed to get all hash fields"))
            }
        }
    }

    async fn h_incr_by(&self, request: Request<HIncrByRequest>) -> Result<Response<HIncrByResponse>, Status> {
        // Check if this is a site replication request (skip_site_replication = true)
        let is_site_replication = request.get_ref().skip_site_replication;

        // Skip auth validation if this is a site replication request or auth is disabled
        if self.auth_service.is_auth_enabled() && !is_site_replication {
            if let Err(status) = self.validate_request_auth(&request).await {
                return Err(status);
            }
        }

        let HIncrByRequest { key, field, value, skip_replication, skip_site_replication: _ } = request.into_inner();

        match self.redis_client.hincr(&key, &field, value).await {
            Ok(new_value) => {
                if skip_replication || self.replication_factor == 0 {
                    return Ok(Response::new(HIncrByResponse { new_value }));
                }

                // Create a replication operation
                let operation = ReplicationOperation {
                    command: "hincrby".to_string(),
                    key: key.clone(),
                    value: value.to_string(),
                    field: Some(field.clone()),
                };

                // Add to batch or replicate immediately based on async setting
                if self.async_replication {
                    // Add to batch for async replication in a background task
                    self.replicate_async_in_background(operation);

                    // Execute site replication if enabled (only for async replication) and not skipped
                    if self.site_replication_enabled && !is_site_replication {
                        let write_op = WriteOperation::HIncrBy { key: key.clone(), field: field.clone(), value };
                        let self_clone = self.clone();
                        tokio::spawn(async move {
                            self_clone.add_operation_to_site_replication(write_op).await;
                        });
                    }

                    Ok(Response::new(HIncrByResponse { new_value }))
                } else {
                    // For synchronous replication, check write consistency with peer Redis nodes
                    let write_op = WriteOperation::HIncrBy { key: key.clone(), field: field.clone(), value };
                    let consistency_success = self.execute_write_with_consistency(write_op).await;

                    if !consistency_success {
                        error!("Write consistency failed for HINCRBY operation on key: {}", key);
                        return Err(Status::internal("Write consistency failed"));
                    }

                    // When async_replication = false, we only do write consistency to peer Redis nodes
                    // No replication to secondary Rust nodes to ensure immediate client response
                    Ok(Response::new(HIncrByResponse { new_value }))
                }
            }
            Err(e) => {
                error!("Failed to increment hash field: {}", e);
                Err(Status::internal("Failed to increment hash field"))
            }
        }
    }

    async fn h_decr_by(&self, request: Request<HDecrByRequest>) -> Result<Response<HDecrByResponse>, Status> {
        // Check if this is a site replication request (skip_site_replication = true)
        let is_site_replication = request.get_ref().skip_site_replication;

        // Skip auth validation if this is a site replication request or auth is disabled
        if self.auth_service.is_auth_enabled() && !is_site_replication {
            if let Err(status) = self.validate_request_auth(&request).await {
                return Err(status);
            }
        }

        let HDecrByRequest { key, field, value, skip_replication, skip_site_replication: _ } = request.into_inner();

        match self.redis_client.hdecrby(&key, &field, value).await {
            Ok(new_value) => {
                if skip_replication || self.replication_factor == 0 {
                    return Ok(Response::new(HDecrByResponse { new_value }));
                }

                // Create a replication operation
                let operation = ReplicationOperation {
                    command: "hdecrby".to_string(),
                    key: key.clone(),
                    value: value.to_string(),
                    field: Some(field.clone()),
                };

                // Add to batch or replicate immediately based on async setting
                if self.async_replication {
                    // Add to batch for async replication in a background task
                    self.replicate_async_in_background(operation);

                    // Execute site replication if enabled (only for async replication) and not skipped
                    if self.site_replication_enabled && !is_site_replication {
                        let write_op = WriteOperation::HDecrBy { key: key.clone(), field: field.clone(), value };
                        let self_clone = self.clone();
                        tokio::spawn(async move {
                            self_clone.add_operation_to_site_replication(write_op).await;
                        });
                    }

                    Ok(Response::new(HDecrByResponse { new_value }))
                } else {
                    // For synchronous replication, check write consistency with peer Redis nodes
                    let write_op = WriteOperation::HDecrBy { key: key.clone(), field: field.clone(), value };
                    let consistency_success = self.execute_write_with_consistency(write_op).await;

                    if !consistency_success {
                        error!("Write consistency failed for HDECRBY operation on key: {}", key);
                        return Err(Status::internal("Write consistency failed"));
                    }

                    // When async_replication = false, we only do write consistency to peer Redis nodes
                    // No replication to secondary Rust nodes to ensure immediate client response
                    Ok(Response::new(HDecrByResponse { new_value }))
                }
            }
            Err(e) => {
                error!("Failed to decrement hash field: {}", e);
                Err(Status::internal("Failed to decrement hash field"))
            }
        }
    }

    async fn h_incr_by_float(&self, request: Request<HIncrByFloatRequest>) -> Result<Response<HIncrByFloatResponse>, Status> {
        // Check if this is a site replication request (skip_site_replication = true)
        let is_site_replication = request.get_ref().skip_site_replication;

        // Skip auth validation if this is a site replication request or auth is disabled
        if self.auth_service.is_auth_enabled() && !is_site_replication {
            if let Err(status) = self.validate_request_auth(&request).await {
                return Err(status);
            }
        }

        let HIncrByFloatRequest { key, field, value, skip_replication, skip_site_replication: _ } = request.into_inner();

        match self.redis_client.hincrbyfloat(&key, &field, value).await {
            Ok(new_value) => {
                if skip_replication || self.replication_factor == 0 {
                    return Ok(Response::new(HIncrByFloatResponse { new_value }));
                }

                // Create a replication operation
                let operation = ReplicationOperation {
                    command: "hincrbyfloat".to_string(),
                    key: key.clone(),
                    value: value.to_string(),
                    field: Some(field.clone()),
                };

                // Add to batch or replicate immediately based on async setting
                if self.async_replication {
                    // Add to batch for async replication in a background task
                    self.replicate_async_in_background(operation);

                    // Execute site replication if enabled (only for async replication) and not skipped
                    if self.site_replication_enabled && !is_site_replication {
                        let write_op = WriteOperation::HIncrByFloat { key: key.clone(), field: field.clone(), value };
                        let self_clone = self.clone();
                        tokio::spawn(async move {
                            self_clone.add_operation_to_site_replication(write_op).await;
                        });
                    }

                    Ok(Response::new(HIncrByFloatResponse { new_value }))
                } else {
                    // For synchronous replication, check write consistency with peer Redis nodes
                    let write_op = WriteOperation::HIncrByFloat { key: key.clone(), field: field.clone(), value };
                    let consistency_success = self.execute_write_with_consistency(write_op).await;

                    if !consistency_success {
                        error!("Write consistency failed for HINCRBYFLOAT operation on key: {}", key);
                        return Err(Status::internal("Write consistency failed"));
                    }

                    // When async_replication = false, we only do write consistency to peer Redis nodes
                    // No replication to secondary Rust nodes to ensure immediate client response
                    Ok(Response::new(HIncrByFloatResponse { new_value }))
                }
            }
            Err(e) => {
                error!("Failed to increment float hash field: {}", e);
                Err(Status::internal("Failed to increment float hash field"))
            }
        }
    }

    fn new_replication_batch(&self, operations: Vec<ReplicationOperation>) -> ReplicationBatch {
        ReplicationBatch::new(operations, self.replication_batch_max_age)
    }

    // Start a background task to periodically flush batches
    fn start_batch_flush_task(&self) {
        // Only start if not already running
        if self.batch_flush_task_running.load(Ordering::SeqCst) == 0 {
            self.batch_flush_task_running.store(1, Ordering::SeqCst);

            let service_clone = self.clone();
            tokio::spawn(async move {
                let mut flush_interval = interval(service_clone.flush_interval);

                loop {
                    flush_interval.tick().await;
                    service_clone.flush_all_batches().await;
                }
            });

            info!("Started batch flush background task with interval {:?}", self.flush_interval);
        }
    }



    // Flush all batches for all nodes
    async fn flush_all_batches(&self) {
        let mut nodes_to_flush = Vec::new();

        // Iterate over all shards
        for shard in &self.batch_collectors.shards {
            let collectors = shard.lock();
            for (node, collector) in collectors.iter() {
                if !collector.is_empty() {
                    nodes_to_flush.push(node.clone());
                }
            }
            // collectors lock is dropped here
        }

        // Process batches in controlled batches to limit concurrent tasks
        if !nodes_to_flush.is_empty() {
            // Limit the number of concurrent flush operations
            const MAX_CONCURRENT_FLUSHES: usize = 4;

            for node_batch in nodes_to_flush.chunks(MAX_CONCURRENT_FLUSHES) {
                let mut flush_futures = Vec::with_capacity(node_batch.len());

                for node in node_batch {
                    let self_clone = self.clone();
                    let node_clone = node.clone();

                    // Spawn a task for each node in this batch
                    let future = tokio::spawn(async move {
                        self_clone.flush_node_batch(&node_clone).await;
                    });

                    flush_futures.push(future);
                }

                // Wait for this batch of flushes to complete before processing the next batch
                let _ = join_all(flush_futures).await;
            }
        }
    }

    /// Flush the batch for a specific node
    ///
    /// Optimized for minimal lock contention and efficient batch processing
    async fn flush_node_batch(&self, node: &str) {
        // Skip if node is empty
        if node.is_empty() {
            return;
        }

        // Take the batch with minimal lock time
        let operations = {
            // First try to get the collector with a try_lock to avoid contention
            // This is a fast path that will succeed most of the time
            if let Some(mut collectors) = self.batch_collectors.try_lock_for_key(node) {
                if let Some(collector) = collectors.get_mut(node) {
                    if collector.is_empty() {
                        return;
                    }
                    collector.take_batch()
                } else {
                    return;
                }
            } else {
                // If try_lock fails, fall back to regular lock
                let mut collectors = self.batch_collectors.lock_for_key(node);
                if let Some(collector) = collectors.get_mut(node) {
                    if collector.is_empty() {
                        return;
                    }
                    collector.take_batch()
                } else {
                    return;
                }
            }
        };

        // Double-check that we have operations to process
        if operations.is_empty() {
            return;
        }

        let batch_size = operations.len();
        debug!("Flushing batch of {} operations to node {}", batch_size, node);

        // Create the batch with the operations
        let batch = self.new_replication_batch(operations);

        // Get a client for this node - use a more efficient approach
        let client_result = {
            let mut node_pool = self.node_pool.write().await;
            node_pool.get_client(node).await
        };

        // Process the batch if we have a client
        match client_result {
            Some(client) => {
                // Send the batch to the client
                self.send_batch_to_client(node, batch, client).await;
            },
            None => {
                error!("Failed to get client for node {}", node);
            }
        }
    }

    /// Send a batch to a client with optimized retry logic
    ///
    /// Uses adaptive backoff and efficient error handling for maximum throughput
    async fn send_batch_to_client(&self, node: &str, batch: ReplicationBatch, mut client: KeyValueServiceClient<Channel>) {
        // Early return if batch is expired to avoid wasted work
        if batch.is_expired() {
            debug!("Batch expired, skipping replication to node {}", node);
            return;
        }

        // Take ownership of operations to avoid clone
        let operations = batch.operations;
        let max_retries = self.max_retries;
        let retry_delay = self.retry_delay;

        // Skip empty batches
        if operations.is_empty() {
            return;
        }

        // Convert operations to BatchOperation proto objects - preallocate with exact capacity
        let mut batch_operations = Vec::with_capacity(operations.len());

        // Use a lookup table for operation types to avoid string comparisons in a hot loop
        // This is more efficient than a match statement for each operation
        let mut op_type_map = HashMap::with_capacity(11);
        op_type_map.insert("set", 0);
        op_type_map.insert("del", 1);
        op_type_map.insert("setex", 2);
        op_type_map.insert("setexpiry", 3);
        op_type_map.insert("incrby", 4);
        op_type_map.insert("decrby", 5);
        op_type_map.insert("incrbyfloat", 6);
        op_type_map.insert("hset", 7);
        op_type_map.insert("hincrby", 8);
        op_type_map.insert("hdecrby", 9);
        op_type_map.insert("hincrbyfloat", 10);

        for op in &operations {
            // Use the lookup table for faster operation type determination
            let operation_type = match op_type_map.get(op.command.as_str()) {
                Some(&op_type) => op_type,
                None => {
                    error!("Unknown command: {}", op.command);
                    continue;
                }
            };

            // Create the batch operation with all fields initialized
            let mut batch_op = BatchOperation {
                operation_type: operation_type.into(),
                key: op.key.clone(),
                value: op.value.clone(),
                field: op.field.clone(),
                int_value: None,
                float_value: None,
                ttl: None,
            };

            // Set additional fields based on operation type - use a more efficient approach
            // Group similar operations together to reduce branching
            match operation_type {
                // Integer operations
                4 | 5 | 8 | 9 => { // INCRBY, DECRBY, HINCRBY, HDECRBY
                    if let Ok(val) = op.value.parse::<i64>() {
                        batch_op.int_value = Some(val);
                    }
                },
                // Float operations
                6 | 10 => { // INCRBYFLOAT, HINCRBYFLOAT
                    if let Ok(val) = op.value.parse::<f64>() {
                        batch_op.float_value = Some(val);
                    }
                },
                // TTL operations
                2 | 3 => { // SETEX, SETEXPIRY
                    if let Ok(val) = op.value.parse::<u64>() {
                        batch_op.ttl = Some(val);
                    }
                },
                _ => {}
            }

            batch_operations.push(batch_op);
        }

        // Create the batch request payload once
        let batch_request = BatchWriteRequest {
            operations: batch_operations,
            skip_replication: true, // Always skip replication to avoid loops
            skip_site_replication: true, // Always skip site replication for internal batches
        };

        // Send the batch request with adaptive backoff retries
        let mut retries = 0;
        // Start with a shorter initial backoff for faster recovery
        let mut backoff_ms = retry_delay.as_millis() as u64 / 2;

        while retries < max_retries {
            // Create a new request for each retry (tonic::Request doesn't implement Clone)
            let request = tonic::Request::new(batch_request.clone());

            match client.batch_write(request).await {
                Ok(_) => {
                    // Only update availability if it's been a while since the last check
                    let mut node_pool = self.node_pool.write().await;
                    if node_pool.last_availability_check.elapsed() > node_pool.availability_check_interval {
                        node_pool.node_availability.insert(node.to_string(), true);
                        node_pool.last_availability_check = std::time::Instant::now();
                    }

                    // Only log at debug level for successful operations after retries
                    if retries > 0 {
                        debug!("Successfully replicated batch to node {} after {} retries", node, retries);
                    }
                    return;
                },
                Err(e) => {
                    retries += 1;

                    if retries < max_retries {
                        // Log at appropriate level based on retry count
                        if retries == 1 {
                            debug!("Failed to replicate batch to {}: {}. Retrying... (attempt {}/{})",
                                   node, e, retries, max_retries);
                        } else {
                            error!("Failed to replicate batch to {}: {}. Retrying... (attempt {}/{})",
                                   node, e, retries, max_retries);
                        }

                        // Use adaptive backoff with jitter for better distribution
                        // Add more jitter for higher retry counts
                        let jitter = (retries as u64 * 3) % 20;
                        let delay = Duration::from_millis(backoff_ms + jitter);
                        sleep(delay).await;

                        // Increase backoff for next retry (capped at 150ms)
                        // Use a more aggressive backoff for higher retry counts
                        backoff_ms = if retries > 2 {
                            (backoff_ms * 3).min(150)
                        } else {
                            (backoff_ms * 2).min(100)
                        };
                    } else {
                        // Mark node as unavailable after all retries failed
                        // This is important enough to always update, regardless of the check interval
                        let mut node_pool = self.node_pool.write().await;
                        node_pool.node_availability.insert(node.to_string(), false);
                        node_pool.last_availability_check = std::time::Instant::now();
                        error!("Failed to replicate batch to {} after {} retries: {}. Marked node as unavailable.",
                               node, max_retries, e);
                    }
                }
            }
        }
    }



    /// Add an operation to batches for all target nodes
    ///
    /// Optimized for minimal cloning and efficient batch management
    async fn add_operation_to_batches(&self, operation: ReplicationOperation) {
        // Skip if no replication is needed
        if self.replication_factor == 0 {
            return;
        }

        // Get available nodes from the node pool
        let available_nodes = {
            let node_pool = self.node_pool.read().await;
            node_pool.get_available_nodes(&self.secondary_nodes, self.replication_factor)
        };

        // Convert to references for processing
        let target_nodes: Vec<&String> = available_nodes.iter().collect();

        // Skip if no target nodes
        if target_nodes.is_empty() {
            return;
        }

        if self.async_replication {
            // Async: send to per-node channel
            // This is more efficient as it offloads batch management to background tasks

            // For a single node, avoid cloning the operation
            if target_nodes.len() == 1 {
                if let Some(sender) = self.op_senders.get(target_nodes[0]) {
                    let _ = sender.send(operation).await;
                }
                return;
            }

            // For multiple nodes, we need to clone for all nodes
            let mut last_node_sender = None;

            // First, send to all nodes except the last one
            for (i, node) in target_nodes.iter().enumerate() {
                if i < target_nodes.len() - 1 {
                    if let Some(sender) = self.op_senders.get(*node) {
                        let _ = sender.send(operation.clone()).await;
                    }
                } else {
                    // Save the last node's sender for later
                    if let Some(sender) = self.op_senders.get(*node) {
                        last_node_sender = Some(sender);
                    }
                }
            }

            // Now send to the last node without cloning
            if let Some(sender) = last_node_sender {
                let _ = sender.send(operation).await;
            }
        } else {
            // Sync: use sharded batch collectors
            // Pre-allocate with exact capacity
            let mut nodes_to_flush = Vec::with_capacity(target_nodes.len());

            // For a single node, avoid cloning the operation
            if target_nodes.len() == 1 {
                let node = target_nodes[0];
                let mut collectors = self.batch_collectors.lock_for_key(node);
                let collector = collectors.entry(node.clone())
                    .or_insert_with(|| BatchCollector::new(self.max_batch_size, self.flush_interval));

                if collector.add_operation(operation) {
                    nodes_to_flush.push(node.clone());
                }
            } else {
                // For multiple nodes, we need to clone for all nodes
                let mut last_node = None;

                // First, process all nodes except the last one
                for (i, node) in target_nodes.iter().enumerate() {
                    if i < target_nodes.len() - 1 {
                        let mut collectors = self.batch_collectors.lock_for_key(node);
                        let collector = collectors.entry((*node).clone())
                            .or_insert_with(|| BatchCollector::new(self.max_batch_size, self.flush_interval));

                        // Clone for all but the last node
                        let should_flush = collector.add_operation(operation.clone());

                        if should_flush {
                            nodes_to_flush.push((*node).clone());
                        }
                    } else {
                        // Save the last node for later
                        last_node = Some(*node);
                    }
                }

                // Now process the last node without cloning
                if let Some(node) = last_node {
                    let mut collectors = self.batch_collectors.lock_for_key(node);
                    let collector = collectors.entry(node.clone())
                        .or_insert_with(|| BatchCollector::new(self.max_batch_size, self.flush_interval));

                    // Move the operation for the last node
                    let should_flush = collector.add_operation(operation);

                    if should_flush {
                        nodes_to_flush.push(node.clone());
                    }
                }
            }

            // Flush batches that reached threshold
            // If there are multiple nodes to flush, process them in parallel
            if nodes_to_flush.len() > 1 {
                let mut futures = Vec::with_capacity(nodes_to_flush.len());

                for node in nodes_to_flush {
                    let self_clone = self.clone();
                    let node_clone = node.clone();

                    futures.push(tokio::spawn(async move {
                        self_clone.flush_node_batch(&node_clone).await;
                    }));
                }

                // Wait for all flushes to complete
                join_all(futures).await;
            } else if nodes_to_flush.len() == 1 {
                // For a single node, avoid the overhead of spawning a task
                self.flush_node_batch(&nodes_to_flush[0]).await;
            }
        }
    }

    // Helper method to handle async replication in a background task
    fn replicate_async_in_background(&self, operation: ReplicationOperation) {
        let self_clone = self.clone();
        tokio::spawn(async move {
            self_clone.add_operation_to_batches(operation).await;
        });
    }

    async fn flush_node_batch_with_ops(&self, node: &str, operations: Vec<ReplicationOperation>) {
        if operations.is_empty() {
            return;
        }
        let batch = self.new_replication_batch(operations);
        let mut node_pool = self.node_pool.write().await;
        let client_result = node_pool.get_client(node).await;
        drop(node_pool);

        if let Some(client) = client_result {
            self.send_batch_to_client(node, batch, client).await;
        } else {
            error!("Failed to get client for node {}", node);
        }
    }
}

#[tonic::async_trait]
impl KeyValueService for KeyValueServiceImpl {
    async fn authenticate(&self, request: Request<AuthenticateRequest>) -> Result<Response<AuthenticateResponse>, Status> {
        self.authenticate(request).await
    }

    async fn ping(&self, request: Request<PingRequest>) -> Result<Response<PingResponse>, Status> {
        self.ping(request).await
    }

    async fn set(&self, request: Request<SetRequest>) -> Result<Response<SetResponse>, Status> {
        self.set(request).await
    }

    async fn get(&self, request: Request<GetRequest>) -> Result<Response<GetResponse>, Status> {
        self.get(request).await
    }

    async fn delete(&self, request: Request<DeleteRequest>) -> Result<Response<DeleteResponse>, Status> {
        self.delete(request).await
    }

    async fn set_ex(&self, request: Request<SetExRequest>) -> Result<Response<SetExResponse>, Status> {
        self.set_ex(request).await
    }

    async fn set_expiry(&self, request: Request<SetExpiryRequest>) -> Result<Response<SetExpiryResponse>, Status> {
        self.set_expiry(request).await
    }

    async fn incr_by(&self, request: Request<IncrByRequest>) -> Result<Response<IncrByResponse>, Status> {
        self.incr_by(request).await
    }

    async fn decr_by(&self, request: Request<DecrByRequest>) -> Result<Response<DecrByResponse>, Status> {
        self.decr_by(request).await
    }

    async fn incr_by_float(&self, request: Request<IncrByFloatRequest>) -> Result<Response<IncrByFloatResponse>, Status> {
        self.incr_by_float(request).await
    }

    async fn h_set(&self, request: Request<HSetRequest>) -> Result<Response<HSetResponse>, Status> {
        self.h_set(request).await
    }

    async fn h_get(&self, request: Request<HGetRequest>) -> Result<Response<HGetResponse>, Status> {
        self.h_get(request).await
    }

    async fn h_get_all(&self, request: Request<HGetAllRequest>) -> Result<Response<HGetAllResponse>, Status> {
        self.h_get_all(request).await
    }

    async fn h_incr_by(&self, request: Request<HIncrByRequest>) -> Result<Response<HIncrByResponse>, Status> {
        self.h_incr_by(request).await
    }

    async fn h_decr_by(&self, request: Request<HDecrByRequest>) -> Result<Response<HDecrByResponse>, Status> {
        self.h_decr_by(request).await
    }

    async fn h_incr_by_float(&self, request: Request<HIncrByFloatRequest>) -> Result<Response<HIncrByFloatResponse>, Status> {
        self.h_incr_by_float(request).await
    }

    // Implement the batch write operation
    async fn batch_write(&self, request: Request<BatchWriteRequest>) -> Result<Response<BatchWriteResponse>, Status> {
        // Check if this is a replication request by peeking at the skip_replication or skip_site_replication fields
        let is_replication_request = request.get_ref().skip_replication;
        let is_site_replication_request = request.get_ref().skip_site_replication;

        // Skip auth validation if this is a replication request (skip_replication = true)
        // or site replication request (skip_site_replication = true) or if auth is disabled.
        // Replication requests come from other nodes and don't have session tokens.
        if !is_replication_request && !is_site_replication_request && self.auth_service.is_auth_enabled() {
            if let Err(status) = self.validate_request_auth(&request).await {
                return Err(status);
            }
        }

        let BatchWriteRequest { operations, skip_replication, skip_site_replication: _ } = request.into_inner();

        if operations.is_empty() {
            return Ok(Response::new(BatchWriteResponse {
                success: true,
                operation_results: vec![],
            }));
        }

        // Pre-allocate vectors with capacity to avoid reallocations
        let ops_count = operations.len();
        let mut set_ops = Vec::with_capacity(ops_count);
        let mut setex_ops = Vec::with_capacity(ops_count);
        let mut del_keys = Vec::with_capacity(ops_count);
        let mut other_ops = Vec::with_capacity(ops_count);
        let mut batch_results = Vec::with_capacity(ops_count);

        // First pass: categorize operations by type for batch processing
        for op in operations {
            match op.operation_type {
                0 => { // SET
                    set_ops.push((op.key.clone(), op.value.clone()));
                },
                1 => { // DELETE
                    del_keys.push(op.key.clone());
                },
                2 => { // SETEX
                    let ttl = op.ttl.unwrap_or(60);
                    setex_ops.push((op.key.clone(), op.value.clone(), ttl));
                },
                _ => {
                    other_ops.push(op);
                }
            }
        }

        // Process batch operations with controlled concurrency
        let mut results: Vec<(usize, usize, bool)> = Vec::with_capacity(3 + other_ops.len());

        // Process bulk operations first (these are already optimized internally)
        if !set_ops.is_empty() {
            let result = self.redis_client.batch_set(&set_ops).await.is_ok();
            results.push((0, set_ops.len(), result));
        }

        if !setex_ops.is_empty() {
            let result = self.redis_client.batch_setex(&setex_ops).await.is_ok();
            results.push((1, setex_ops.len(), result));
        }

        if !del_keys.is_empty() {
            let result = self.redis_client.batch_del(&del_keys).await.is_ok();
            results.push((2, del_keys.len(), result));
        }

        // Process other operations in smaller batches to limit concurrency
        if !other_ops.is_empty() {
            const MAX_CONCURRENT_OPS: usize = 8;

            for ops_batch in other_ops.chunks(MAX_CONCURRENT_OPS) {
                let mut futures = Vec::with_capacity(ops_batch.len());

                for (i, op) in ops_batch.iter().enumerate() {
                    let redis_client = self.redis_client.clone();
                    let op_clone = op.clone();
                    let base_index = i;

                    futures.push(tokio::spawn(async move {
                        let result = match op_clone.operation_type {
                            3 => { // SETEXPIRY
                                let ttl = op_clone.ttl.unwrap_or(60) as i64;
                                redis_client.set_expiry(&op_clone.key, ttl).await.is_ok()
                            },
                            4 => { // INCRBY
                                let value = op_clone.int_value.unwrap_or(1);
                                redis_client.incr(&op_clone.key, value).await.is_ok()
                            },
                            5 => { // DECRBY
                                let value = op_clone.int_value.unwrap_or(1);
                                redis_client.decr(&op_clone.key, value).await.is_ok()
                            },
                            6 => { // INCRBYFLOAT
                                let value = op_clone.float_value.unwrap_or(1.0);
                                redis_client.incrbyfloat(&op_clone.key, value).await.is_ok()
                            },
                            7 => { // HSET
                                if let Some(field) = &op_clone.field {
                                    redis_client.hset(&op_clone.key, field, &op_clone.value).await.is_ok()
                                } else {
                                    error!("HSET operation missing field");
                                    false
                                }
                            },
                            8 => { // HINCRBY
                                if let Some(field) = &op_clone.field {
                                    let value = op_clone.int_value.unwrap_or(1);
                                    redis_client.hincr(&op_clone.key, field, value).await.is_ok()
                                } else {
                                    error!("HINCRBY operation missing field");
                                    false
                                }
                            },
                            9 => { // HDECRBY
                                if let Some(field) = &op_clone.field {
                                    let value = op_clone.int_value.unwrap_or(1);
                                    redis_client.hdecrby(&op_clone.key, field, value).await.is_ok()
                                } else {
                                    error!("HDECRBY operation missing field");
                                    false
                                }
                            },
                            10 => { // HINCRBYFLOAT
                                if let Some(field) = &op_clone.field {
                                    let value = op_clone.float_value.unwrap_or(1.0);
                                    redis_client.hincrbyfloat(&op_clone.key, field, value).await.is_ok()
                                } else {
                                    error!("HINCRBYFLOAT operation missing field");
                                    false
                                }
                            },
                            _ => {
                                error!("Unknown operation type: {}", op_clone.operation_type);
                                false
                            }
                        };

                        (3, base_index, result) // Type 3 = other op, i = index in other_ops
                    }));
                }

                // Wait for this batch to complete
                for future in join_all(futures).await {
                    if let Ok(result) = future {
                        results.push(result);
                    } else {
                        // Handle task join error
                        error!("Task join error in batch_write");
                        results.push((3, 0, false));
                    }
                }
            }
        }

        // Process results
        let mut overall_success = true;
        let mut batch_results_map: HashMap<(usize, usize), bool> = HashMap::new();

        // Add all results to the map
        for (op_type, index, result) in results {
            batch_results_map.insert((op_type, index), result);
            if !result {
                overall_success = false;
            }
        }

        // Collect results in the original order
        if !set_ops.is_empty() {
            let result = batch_results_map.get(&(0, set_ops.len())).copied().unwrap_or(false);
            batch_results.extend(vec![result; set_ops.len()]);
        }

        if !setex_ops.is_empty() {
            let result = batch_results_map.get(&(1, setex_ops.len())).copied().unwrap_or(false);
            batch_results.extend(vec![result; setex_ops.len()]);
        }

        if !del_keys.is_empty() {
            let result = batch_results_map.get(&(2, del_keys.len())).copied().unwrap_or(false);
            batch_results.extend(vec![result; del_keys.len()]);
        }

        for i in 0..other_ops.len() {
            let result = batch_results_map.get(&(3, i)).copied().unwrap_or(false);
            batch_results.push(result);
        }

        // Handle replication if skip_replication is false and we have successful operations
        if !skip_replication && self.replication_factor > 0 && overall_success {
            // Clone data for site replication before it gets moved
            let set_ops_clone = if self.site_replication_enabled && !is_site_replication_request {
                Some(set_ops.clone())
            } else {
                None
            };
            let setex_ops_clone = if self.site_replication_enabled && !is_site_replication_request {
                Some(setex_ops.clone())
            } else {
                None
            };
            let del_keys_clone = if self.site_replication_enabled && !is_site_replication_request {
                Some(del_keys.clone())
            } else {
                None
            };
            let other_ops_clone = if self.site_replication_enabled && !is_site_replication_request {
                Some(other_ops.clone())
            } else {
                None
            };
            let batch_results_map_clone = if self.site_replication_enabled && !is_site_replication_request {
                Some(batch_results_map.clone())
            } else {
                None
            };

            // Create replication operations for successful operations
            let mut replication_operations = Vec::new();

            // Add SET operations
            if !set_ops.is_empty() {
                let set_result = batch_results_map.get(&(0, set_ops.len())).copied().unwrap_or(false);
                if set_result {
                    for (key, value) in set_ops {
                        replication_operations.push(ReplicationOperation {
                            command: "set".to_string(),
                            key,
                            value,
                            field: None,
                        });
                    }
                }
            }

            // Add SETEX operations
            if !setex_ops.is_empty() {
                let setex_result = batch_results_map.get(&(1, setex_ops.len())).copied().unwrap_or(false);
                if setex_result {
                    for (key, value, _ttl) in setex_ops {
                        replication_operations.push(ReplicationOperation {
                            command: "setex".to_string(),
                            key,
                            value,
                            field: None,
                        });
                    }
                }
            }

            // Add DELETE operations
            if !del_keys.is_empty() {
                let del_result = batch_results_map.get(&(2, del_keys.len())).copied().unwrap_or(false);
                if del_result {
                    for key in del_keys {
                        replication_operations.push(ReplicationOperation {
                            command: "del".to_string(),
                            key,
                            value: String::new(),
                            field: None,
                        });
                    }
                }
            }

            // Add other operations (only if they were successful)
            for (i, op) in other_ops.iter().enumerate() {
                let result = batch_results_map.get(&(3, i)).copied().unwrap_or(false);
                if result {
                    let command = match op.operation_type {
                        3 => "setexpiry",
                        4 => "incrby",
                        5 => "decrby",
                        6 => "incrbyfloat",
                        7 => "hset",
                        8 => "hincrby",
                        9 => "hdecrby",
                        10 => "hincrbyfloat",
                        _ => continue, // Skip unknown operations
                    };

                    replication_operations.push(ReplicationOperation {
                        command: command.to_string(),
                        key: op.key.clone(),
                        value: op.value.clone(),
                        field: op.field.clone(),
                    });
                }
            }

            // Replicate operations if we have any
            if !replication_operations.is_empty() {
                if self.async_replication {
                    // Add to batch for async replication in a background task
                    let self_clone = self.clone();
                    tokio::spawn(async move {
                        for operation in replication_operations {
                            self_clone.add_operation_to_batches(operation).await;
                        }
                    });

                    // Execute site replication if enabled (only for async replication) and not skipped
                    if self.site_replication_enabled && !is_site_replication_request {
                        let self_clone = self.clone();
                        tokio::spawn(async move {
                            // Convert ReplicationOperations to WriteOperations for site replication
                            let mut site_operations = Vec::new();

                            // Add SET operations
                            if let Some(set_ops_clone) = set_ops_clone {
                                if !set_ops_clone.is_empty() {
                                    let set_result = batch_results_map_clone.as_ref().unwrap().get(&(0, set_ops_clone.len())).copied().unwrap_or(false);
                                    if set_result {
                                        for (key, value) in set_ops_clone {
                                            site_operations.push(WriteOperation::Set {
                                                key,
                                                value
                                            });
                                        }
                                    }
                                }
                            }

                            // Add SETEX operations
                            if let Some(setex_ops_clone) = setex_ops_clone {
                                if !setex_ops_clone.is_empty() {
                                    let setex_result = batch_results_map_clone.as_ref().unwrap().get(&(1, setex_ops_clone.len())).copied().unwrap_or(false);
                                    if setex_result {
                                        for (key, value, ttl) in setex_ops_clone {
                                            site_operations.push(WriteOperation::SetEx {
                                                key,
                                                value,
                                                ttl
                                            });
                                        }
                                    }
                                }
                            }

                            // Add DELETE operations
                            if let Some(del_keys_clone) = del_keys_clone {
                                if !del_keys_clone.is_empty() {
                                    let del_result = batch_results_map_clone.as_ref().unwrap().get(&(2, del_keys_clone.len())).copied().unwrap_or(false);
                                    if del_result {
                                        for key in del_keys_clone {
                                            site_operations.push(WriteOperation::Delete {
                                                key
                                            });
                                        }
                                    }
                                }
                            }

                            // Add other operations
                            if let Some(other_ops_clone) = other_ops_clone {
                                for (i, op) in other_ops_clone.iter().enumerate() {
                                    let result = batch_results_map_clone.as_ref().unwrap().get(&(3, i)).copied().unwrap_or(false);
                                    if result {
                                        let write_op = match op.operation_type {
                                        3 => WriteOperation::SetExpiry {
                                            key: op.key.clone(),
                                            ttl: op.int_value.unwrap_or(0)
                                        },
                                        4 => WriteOperation::IncrBy {
                                            key: op.key.clone(),
                                            value: op.int_value.unwrap_or(1)
                                        },
                                        5 => WriteOperation::DecrBy {
                                            key: op.key.clone(),
                                            value: op.int_value.unwrap_or(1)
                                        },
                                        6 => WriteOperation::IncrByFloat {
                                            key: op.key.clone(),
                                            value: op.float_value.unwrap_or(1.0)
                                        },
                                        7 => {
                                            if let Some(field) = &op.field {
                                                WriteOperation::HSet {
                                                    key: op.key.clone(),
                                                    field: field.clone(),
                                                    value: op.value.clone()
                                                }
                                            } else {
                                                continue;
                                            }
                                        },
                                        8 => {
                                            if let Some(field) = &op.field {
                                                WriteOperation::HIncrBy {
                                                    key: op.key.clone(),
                                                    field: field.clone(),
                                                    value: op.int_value.unwrap_or(1)
                                                }
                                            } else {
                                                continue;
                                            }
                                        },
                                        9 => {
                                            if let Some(field) = &op.field {
                                                WriteOperation::HDecrBy {
                                                    key: op.key.clone(),
                                                    field: field.clone(),
                                                    value: op.int_value.unwrap_or(1)
                                                }
                                            } else {
                                                continue;
                                            }
                                        },
                                        10 => {
                                            if let Some(field) = &op.field {
                                                WriteOperation::HIncrByFloat {
                                                    key: op.key.clone(),
                                                    field: field.clone(),
                                                    value: op.float_value.unwrap_or(1.0)
                                                }
                                            } else {
                                                continue;
                                            }
                                        },
                                        _ => continue, // Skip unknown operations
                                        };
                                        site_operations.push(write_op);
                                    }
                                }
                            }

                            // Send site operations to site replication batch
                            if !site_operations.is_empty() {
                                for operation in site_operations {
                                    self_clone.add_operation_to_site_replication(operation).await;
                                }
                            }
                        });
                    }
                } else {
                    // Create a batch with all operations for immediate replication
                    let batch = ReplicationBatch::new(replication_operations, self.replication_batch_max_age);
                    self.replicate_batch(batch).await;
                }
            }
        }

        Ok(Response::new(BatchWriteResponse {
            success: overall_success,
            operation_results: batch_results,
        }))
    }
}

/// Optimized sharded batch collectors with improved locking strategy
///
/// Uses a more efficient hashing algorithm and lock-free operations where possible
struct ShardedBatchCollectors {
    shards: Vec<Mutex<HashMap<String, BatchCollector>>>,
    // Cache the number of shards to avoid repeated calculations
    num_shards: usize,
}

impl ShardedBatchCollectors {
    fn new(num_shards: usize) -> Self {
        // Ensure we have at least 64 shards to reduce contention
        // Increased from 32 to 64 for better parallelism
        let num_shards = num_shards.max(64);

        // Use a power of 2 for more efficient modulo operations
        // This allows the compiler to optimize the modulo to a bitwise AND
        let num_shards = num_shards.next_power_of_two();

        debug!("Creating ShardedBatchCollectors with {} shards", num_shards);

        let mut shards = Vec::with_capacity(num_shards);
        for _ in 0..num_shards {
            // Pre-allocate with a reasonable capacity to avoid rehashing
            // Increased from 32 to 64 for better initial capacity
            let map = HashMap::with_capacity(64);
            shards.push(Mutex::new(map));
        }
        Self {
            shards,
            num_shards,
        }
    }

    #[inline]
    fn shard_for_key(&self, key: &str) -> usize {
        // Use a faster hashing approach for better distribution
        // The inline attribute helps the compiler optimize this hot path
        let mut hasher = DefaultHasher::new();
        key.hash(&mut hasher);

        // Since num_shards is a power of 2, this is equivalent to % num_shards
        // but much faster as it gets optimized to a bitwise AND operation
        (hasher.finish() as usize) & (self.num_shards - 1)
    }

    // Get a lock for a key's shard - now synchronous, not async
    #[inline]
    fn lock_for_key(&self, key: &str) -> parking_lot::MutexGuard<'_, HashMap<String, BatchCollector>> {
        let idx = self.shard_for_key(key);
        self.shards[idx].lock()
    }

    // Try to get a lock without waiting - returns None if lock is already held
    #[inline]
    fn try_lock_for_key(&self, key: &str) -> Option<parking_lot::MutexGuard<'_, HashMap<String, BatchCollector>>> {
        let idx = self.shard_for_key(key);
        self.shards[idx].try_lock()
    }

    // Get locks for multiple keys at once, avoiding deadlocks
    fn lock_for_keys<'a>(&'a self, keys: &[&str]) -> Vec<(usize, parking_lot::MutexGuard<'a, HashMap<String, BatchCollector>>)> {
        if keys.is_empty() {
            return Vec::new();
        }

        // Calculate shard indices for all keys
        let mut indices: Vec<usize> = keys.iter()
            .map(|k| self.shard_for_key(k))
            .collect();

        // Deduplicate indices to avoid double-locking the same shard
        indices.sort_unstable();
        indices.dedup();

        // Acquire locks in order of shard index to prevent deadlocks
        let mut locks = Vec::with_capacity(indices.len());
        for &idx in &indices {
            locks.push((idx, self.shards[idx].lock()));
        }

        locks
    }
}