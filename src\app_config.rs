use serde::Deserialize;
use log::info;
use num_cpus;

#[derive(Deserialize, Debug)]
pub struct Config {
    pub redis_url: String,
    pub secondary_nodes: Vec<String>,
    pub port: u16,
    pub replication_factor: usize, // to how many secondary nodes to replicate
    pub read_consistency: usize, // if the read consistency is 2, it means that the data should be replicated to at least 2 secondary nodes before being considered valid
    #[serde(default = "default_async_replication")]
    pub async_replication: bool, // if true, the data will be replicated to the secondary nodes asynchronously
    #[serde(default = "default_max_retries")]
    pub max_retries: u32, // maximum number of retries for connection attempts
    #[serde(default = "default_retry_delay_ms")]
    pub retry_delay_ms: u64, // delay between retries in milliseconds
    #[serde(default = "default_replication_batch_max_age_secs")]
    pub replication_batch_max_age_secs: u64, // maximum age of a replication batch in seconds
    #[serde(default = "default_redis_pool_size")]
    pub redis_pool_size: usize,
    #[serde(default = "default_secondary_pool_size")]
    pub secondary_pool_size: usize,
    #[serde(default = "default_max_batch_size")]
    pub max_batch_size: usize, // maximum number of operations in a batch
    #[serde(default = "default_batch_flush_interval_ms")]
    pub batch_flush_interval_ms: u64, // interval in milliseconds to flush batches
    #[serde(default = "default_tcp_keepalive_secs")]
    pub tcp_keepalive_secs: u64,
    #[serde(default = "default_tcp_nodelay")]
    pub tcp_nodelay: bool,
    #[serde(default = "default_concurrency_limit")]
    pub concurrency_limit: usize,
    #[serde(default = "default_max_concurrent_streams")]
    pub max_concurrent_streams: u32,
    #[serde(default = "default_chunk_size")]
    pub chunk_size: usize,
    #[serde(default = "default_num_shards")]
    pub num_shards: usize,
    #[serde(default = "default_worker_threads")]
    pub worker_threads: usize,
    // Authentication configuration
    #[serde(default = "default_auth_enabled")]
    pub auth_enabled: bool,
    #[serde(default = "default_auth_username")]
    pub auth_username: String,
    #[serde(default = "default_auth_password")]
    pub auth_password: String,
    #[serde(default = "default_session_duration_secs")]
    pub session_duration_secs: u64,
    // Write consistency configuration
    #[serde(default = "default_peer_redis_nodes")]
    pub peer_redis_nodes: Vec<String>, // list of other Redis nodes for write consistency
    #[serde(default = "default_write_consistency")]
    pub write_consistency: String, // ALL, QUORUM, or ONE
    #[serde(default = "default_quorum_value")]
    pub quorum_value: usize, // number of nodes expected for quorum
    #[serde(default = "default_write_retry_count")]
    pub write_retry_count: u32, // retry attempts for write consistency
    #[serde(default = "default_peer_redis_pool_size")]
    pub peer_redis_pool_size: usize, // connection pool size for each peer Redis node
    // Site-based replication configuration
    #[serde(default = "default_site_replication_enabled")]
    pub site_replication_enabled: bool, // enable site-based replication
    #[serde(default = "default_site_primary_node")]
    pub site_primary_node: String, // primary node for other site
    #[serde(default = "default_site_failover_node")]
    pub site_failover_node: String, // failover node for other site
    #[serde(default = "default_site_replication_retry_count")]
    pub site_replication_retry_count: u32, // retry attempts for site replication
    #[serde(default = "default_site_replication_timeout_ms")]
    pub site_replication_timeout_ms: u64, // timeout for site replication operations
    #[serde(default = "default_site_replication_pool_size")]
    pub site_replication_pool_size: usize, // connection pool size for each site node
    #[serde(default = "default_use_physical_connections")]
    pub use_physical_connections: bool, // if true, create actual TCP connections; if false, use HTTP/2 multiplexing
}

// Default configuration values - minimal baseline that can be increased through configuration
fn default_async_replication() -> bool { true }
fn default_max_retries() -> u32 { 3 }
fn default_retry_delay_ms() -> u64 { 50 }
fn default_replication_batch_max_age_secs() -> u64 { 30 }
fn default_redis_pool_size() -> usize { 32 }
fn default_secondary_pool_size() -> usize { 16 }
fn default_max_batch_size() -> usize { 1000 }
fn default_batch_flush_interval_ms() -> u64 { 50 }
fn default_tcp_keepalive_secs() -> u64 { 30 }
fn default_tcp_nodelay() -> bool { true }
fn default_concurrency_limit() -> usize { 64 }
fn default_max_concurrent_streams() -> u32 { 256 }
fn default_chunk_size() -> usize { 1000 }
fn default_num_shards() -> usize { 16 }
fn default_worker_threads() -> usize {
    // Use number of logical cores by default, with a minimum of 4 and maximum of 64
    let cores = match num_cpus::get() {
        0 => 4, // Fallback if num_cpus returns 0 for some reason
        cores => cores,
    };
    std::cmp::min(64, std::cmp::max(4, cores))
}
fn default_auth_enabled() -> bool { false }
fn default_auth_username() -> String { String::new() }
fn default_auth_password() -> String { String::new() }
fn default_session_duration_secs() -> u64 { 3600 } // 1 hour
fn default_peer_redis_nodes() -> Vec<String> { Vec::new() }
fn default_write_consistency() -> String { "QUORUM".to_string() }
fn default_quorum_value() -> usize { 2 }
fn default_write_retry_count() -> u32 { 3 }
fn default_peer_redis_pool_size() -> usize { 64 }
fn default_site_replication_enabled() -> bool { false }
fn default_site_primary_node() -> String { String::new() }
fn default_site_failover_node() -> String { String::new() }
fn default_site_replication_retry_count() -> u32 { 3 }
fn default_site_replication_timeout_ms() -> u64 { 5000 }
fn default_site_replication_pool_size() -> usize { 256 }
fn default_use_physical_connections() -> bool { true }

impl Config {
    pub fn from_file(file_path: Option<&str>) -> Self {
        let config_file = file_path.unwrap_or("config.toml"); // Use the provided file path or default to "config.toml"
        let settings = config::Config::builder()
            .add_source(config::File::with_name(config_file).required(true))
            .build()
            .expect("Failed to load configuration file");
        let config: Config = settings
            .try_deserialize::<Config>() // Explicitly deserialize into the Config struct
            .expect("Failed to parse configuration");

        // Validate configuration
        if config.redis_url.is_empty() {
            panic!("Redis URL cannot be empty");
        } else {
            // For security, don't log the full URL if it contains authentication
            let has_auth = config.redis_url.contains('@');

            if has_auth {
                // Log Redis URL without exposing credentials
                let parts: Vec<&str> = config.redis_url.split('@').collect();
                if parts.len() > 1 {
                    let auth_part = parts[0];
                    let host_part = parts[1];

                    // Check authentication format
                    if auth_part.contains(':') {
                        let colon_count = auth_part.chars().filter(|&c| c == ':').count();

                        if colon_count > 1 {
                            // Format: redis://username:password@host:port
                            info!("Redis URL: redis://username:******@{} (username+password auth)", host_part);
                        } else if auth_part.starts_with("redis://:") {
                            // Format: redis://:password@host:port (password only)
                            info!("Redis URL: redis://:******@{} (password-only auth)", host_part);
                        } else {
                            // Format: redis://username:password@host:port
                            info!("Redis URL: redis://******:******@{} (username+password auth)", host_part);
                        }
                    } else {
                        // Format: redis://username@host:port (username only)
                        info!("Redis URL: redis://******@{} (username-only auth)", host_part);
                    }
                } else {
                    // Malformed URL but has @ symbol
                    info!("Redis URL: {} (malformed auth URL)", config.redis_url);
                }
            } else {
                // No authentication
                info!("Redis URL: {} (no authentication)", config.redis_url);
            }
        }
        if config.secondary_nodes.is_empty() {
            panic!("Secondary nodes cannot be empty");
        }else{
            info!("Secondary nodes: {:?}", config.secondary_nodes);
        }
        if config.redis_pool_size == 0 {
            panic!("Pool size must be greater than 0");
        } else{
            info!("Redis pool size: {}", config.redis_pool_size);
        }
        if config.replication_factor > config.secondary_nodes.len() {
            panic!("Replication factor cannot exceed the number of secondary nodes");
        } else{
            info!("Replication factor: {}", config.replication_factor);
        }
        if config.read_consistency > config.secondary_nodes.len() {
            panic!("Read consistency cannot exceed the number of secondary nodes");
        } else{
            info!("Read consistency: {}", config.read_consistency);
        }
        if config.max_retries == 0 {
            panic!("Max retries must be greater than 0");
        } else{
            info!("Max retries: {}", config.max_retries);
        }
        if config.retry_delay_ms == 0 {
            panic!("Retry delay must be greater than 0");
        } else {
            info!("Retry delay: {} ms", config.retry_delay_ms);
        }
        if config.replication_batch_max_age_secs == 0 {
            panic!("Replication batch max age must be greater than 0");
        } else{
            info!("Replication batch max age: {} seconds", config.replication_batch_max_age_secs);
        }
        if config.max_batch_size == 0 {
            panic!("Max batch size must be greater than 0");
        } else{
            info!("Max batch size: {}", config.max_batch_size);
        }
        if config.batch_flush_interval_ms == 0 {
            panic!("Batch flush interval must be greater than 0");
        } else{
            info!("Batch flush interval: {} ms", config.batch_flush_interval_ms);
        }
        if config.tcp_keepalive_secs == 0 {
            panic!("TCP keepalive seconds must be greater than 0");
        } else{
            info!("TCP keepalive seconds: {}", config.tcp_keepalive_secs);
        }
        if config.concurrency_limit == 0 {
            panic!("Concurrency limit must be greater than 0");
        } else{
            info!("Concurrency limit: {}", config.concurrency_limit);
        }
        if config.max_concurrent_streams == 0 {
            panic!("Max concurrent streams must be greater than 0");
        } else{
            info!("Max concurrent streams: {}", config.max_concurrent_streams);
        }
        if config.chunk_size == 0 {
            panic!("Chunk size must be greater than 0");
        } else{
            info!("Chunk size: {}", config.chunk_size);
        }
        if config.num_shards == 0 {
            panic!("Number of shards must be greater than 0");
        } else{
            info!("Number of shards: {}", config.num_shards);
        }
        if config.worker_threads == 0 {
            panic!("Worker threads must be greater than 0");
        } else{
            info!("Worker threads: {}", config.worker_threads);
        }
        if config.secondary_pool_size == 0 {
            panic!("Secondary pool size must be greater than 0");
        } else{
            info!("Secondary pool size: {}", config.secondary_pool_size);
        }
        info!("TCP nodelay: {}", config.tcp_nodelay);
        info!("Async replication: {}", config.async_replication);

        // Validate write consistency configuration
        if !config.write_consistency.is_empty() {
            match config.write_consistency.to_uppercase().as_str() {
                "ALL" | "QUORUM" | "ONE" => {
                    info!("Write consistency: {}", config.write_consistency);
                },
                _ => {
                    panic!("Invalid write_consistency value. Must be ALL, QUORUM, or ONE");
                }
            }
        }

        if config.quorum_value == 0 {
            panic!("Quorum value must be greater than 0");
        } else {
            info!("Quorum value: {}", config.quorum_value);
        }

        if config.write_retry_count == 0 {
            panic!("Write retry count must be greater than 0");
        } else {
            info!("Write retry count: {}", config.write_retry_count);
        }

        if config.peer_redis_pool_size == 0 {
            panic!("Peer Redis pool size must be greater than 0");
        } else if config.peer_redis_pool_size > 2048 {
            panic!("Peer Redis pool size cannot exceed 2048 connections per node");
        } else {
            info!("Peer Redis pool size: {}", config.peer_redis_pool_size);
        }

        if !config.peer_redis_nodes.is_empty() {
            info!("Peer Redis nodes: {:?}", config.peer_redis_nodes);

            // Validate that quorum_value doesn't exceed the number of peer nodes
            if config.write_consistency.to_uppercase() == "QUORUM" &&
               config.quorum_value > config.peer_redis_nodes.len() {
                panic!("Quorum value cannot exceed the number of peer Redis nodes");
            }
        } else {
            info!("No peer Redis nodes configured - write consistency disabled");
        }

        // Validate site replication configuration
        if config.site_replication_enabled {
            if config.site_primary_node.is_empty() && config.site_failover_node.is_empty() {
                panic!("Site replication enabled but no primary or failover nodes configured");
            }

            if config.site_replication_pool_size == 0 {
                panic!("Site replication pool size must be greater than 0");
            } else if config.site_replication_pool_size > 2048 {
                panic!("Site replication pool size cannot exceed 2048 connections per node");
            } else {
                info!("Site replication pool size: {}", config.site_replication_pool_size);
            }

            if config.site_replication_retry_count == 0 {
                panic!("Site replication retry count must be greater than 0");
            } else {
                info!("Site replication retry count: {}", config.site_replication_retry_count);
            }

            if config.site_replication_timeout_ms == 0 {
                panic!("Site replication timeout must be greater than 0");
            } else {
                info!("Site replication timeout: {} ms", config.site_replication_timeout_ms);
            }

            info!("Site replication enabled with primary: {} and failover: {}",
                  config.site_primary_node, config.site_failover_node);
        } else {
            info!("Site replication disabled");
        }

        config
    }
}
